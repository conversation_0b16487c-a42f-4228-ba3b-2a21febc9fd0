using Microsoft.Extensions.Logging;
using ThuneeAPI.Application.DTOs;
using ThuneeAPI.Application.Interfaces;
using ThuneeAPI.Core.Entities;
using ThuneeAPI.Infrastructure.Data.Repositories;

namespace ThuneeAPI.Infrastructure.Services;

// Helper class for stored procedure results
public class CompetitionTeamWithUserInfo
{
    public Guid Id { get; set; }
    public string TeamName { get; set; } = string.Empty;
    public Guid Player1Id { get; set; }
    public Guid? Player2Id { get; set; }
    public string InviteCode { get; set; } = string.Empty;
    public int GamesPlayed { get; set; }
    public int Points { get; set; }
    public int BonusPoints { get; set; }
    public int TotalPoints { get; set; }
    public int MaxGames { get; set; }
    public bool IsActive { get; set; }
    public bool IsComplete { get; set; }
    public DateTime RegisteredAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public string Phase { get; set; } = string.Empty;
    public bool IsEliminated { get; set; }
    public bool AdvancedToNextPhase { get; set; }
    public DateTime? PhaseEliminatedAt { get; set; }
    public string? Player1Username { get; set; }
    public string? Player2Username { get; set; }
}

// Static class for SQL command names
public static class SqlCommands
{
    public const string SP_AdvanceCompetitionPhase = "SP_AdvanceCompetitionPhase";
    public const string SP_GetEligibleTeamsForPhase = "SP_GetEligibleTeamsForPhase";
    public const string SP_GetTeamsByPhase = "SP_GetTeamsByPhase";
    public const string SP_CheckTeamEligibilityForGames = "SP_CheckTeamEligibilityForGames";
    public const string SP_CreateCompetitionPhaseLobby = "SP_CreateCompetitionPhaseLobby";
    public const string SP_SetPhaseLobbyWinner = "SP_SetPhaseLobbyWinner";
}

// Stored Procedures class for knockout competition
public static class StoredProcedures
{
    public const string SP_CreatePhaseLobby = "SP_CreatePhaseLobby";
    public const string SP_GetPhaseLobbyDetails = "SP_GetPhaseLobbyDetails";
    public const string SP_ProcessGameResult = "SP_ProcessGameResult";
    public const string SP_GetPhaseLobbies = "SP_GetPhaseLobbies";
    public const string SP_GetPhaseWinners = "SP_GetPhaseWinners";
    public const string SP_AdvancePhase = "SP_AdvancePhase";
    public const string SP_GetAdminLobbyView = "SP_GetAdminLobbyView";
}

public class CompetitionPhaseService : ICompetitionPhaseService
{
    private readonly ILogger<CompetitionPhaseService> _logger;
    private readonly ICompetitionRepository _competitionRepository;
    private readonly ICompetitionTeamRepository _competitionTeamRepository;
    private readonly IUserRepository _userRepository;
    private readonly IEmailService _emailService;

    public CompetitionPhaseService(
        ILogger<CompetitionPhaseService> logger,
        ICompetitionRepository competitionRepository,
        ICompetitionTeamRepository competitionTeamRepository,
        IUserRepository userRepository,
        IEmailService emailService)
    {
        _logger = logger;
        _competitionRepository = competitionRepository;
        _competitionTeamRepository = competitionTeamRepository;
        _userRepository = userRepository;
        _emailService = emailService;
    }

    public async Task<CompetitionDto> AdvanceCompetitionPhaseAsync(Guid competitionId, string newPhase)
    {
        _logger.LogInformation("Advancing competition {CompetitionId} to phase {Phase}", competitionId, newPhase);

        var competition = await _competitionRepository.GetByIdAsync(competitionId);
        if (competition == null)
            throw new ArgumentException("Competition not found");

        // Get eligible teams for the new phase
        var eligibleTeams = await GetEligibleTeamsForNextPhaseAsync(competitionId);
        var eligibleTeamIds = eligibleTeams.Select(t => t.Id).ToList();

        _logger.LogInformation("Found {Count} eligible teams for phase {Phase}", eligibleTeams.Count, newPhase);

        try
        {
            // Use the stored procedure to advance competition phase and manage teams
            var spParameters = new
            {
                CompetitionId = competitionId,
                NewPhase = newPhase,
                EligibleTeamIds = string.Join(",", eligibleTeamIds), // Pass as comma-separated string
                PhaseEndDate = GetPhaseEndDate(newPhase),
                MaxGamesPerPhase = GetMaxGamesForPhase(newPhase)
            };

            _logger.LogInformation("Executing SP_AdvanceCompetitionPhase for competition {CompetitionId} to phase {Phase} with {EligibleCount} teams",
                competitionId, newPhase, eligibleTeams.Count);

            Console.WriteLine($"[DEBUG] About to execute SP_AdvanceCompetitionPhase with parameters: CompetitionId={competitionId}, NewPhase={newPhase}, EligibleTeamIds={spParameters.EligibleTeamIds}");

            // Execute the stored procedure
            var affectedRows = await _competitionRepository.ExecuteStoredProcedureAsync("SP_AdvanceCompetitionPhase", spParameters);

            Console.WriteLine($"[DEBUG] SP_AdvanceCompetitionPhase executed. Affected rows: {affectedRows}");
            _logger.LogInformation("SP_AdvanceCompetitionPhase executed successfully for competition {CompetitionId}. Affected rows: {AffectedRows}", competitionId, affectedRows);

            // Refresh the competition data from database to get updated values
            var updatedCompetition = await _competitionRepository.GetByIdAsync(competitionId);
            if (updatedCompetition == null)
                throw new InvalidOperationException("Competition not found after update");

            _logger.LogInformation("Successfully advanced competition {CompetitionId} to phase {Phase} with {EligibleCount} teams advanced and {EliminatedCount} teams eliminated",
                competitionId, newPhase, eligibleTeams.Count, eligibleTeamIds.Count - eligibleTeams.Count);

            return MapToCompetitionDto(updatedCompetition);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error advancing competition {CompetitionId} to phase {Phase}", competitionId, newPhase);
            throw;
        }
    }

    public async Task<List<CompetitionTeamDto>> GetTeamsForPhaseAsync(Guid competitionId, string phase)
    {
        _logger.LogInformation("Getting teams for competition {CompetitionId} phase {Phase}", competitionId, phase);

        try
        {
            var teams = await _competitionTeamRepository.GetByCompetitionIdAsync(competitionId);
            var phaseTeams = teams.Where(t => t.Phase == phase && !t.IsEliminated).ToList();

            var teamDtos = new List<CompetitionTeamDto>();
            foreach (var team in phaseTeams)
            {
                var teamDto = await MapToCompetitionTeamDtoAsync(team);
                teamDtos.Add(teamDto);
            }

            _logger.LogInformation("Found {Count} teams in phase {Phase} for competition {CompetitionId}",
                teamDtos.Count, phase, competitionId);

            return teamDtos;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting teams for phase {Phase} in competition {CompetitionId}", phase, competitionId);
            throw;
        }
    }

    public async Task<List<CompetitionTeamDto>> GetEligibleTeamsForNextPhaseAsync(Guid competitionId)
    {
        _logger.LogInformation("Getting eligible teams for next phase in competition {CompetitionId}", competitionId);

        var competition = await _competitionRepository.GetByIdAsync(competitionId);
        if (competition == null)
            throw new ArgumentException("Competition not found");

        var currentPhase = competition.Phase ?? "Leaderboard";
        var advancingCount = GetAdvancingCountForPhase(currentPhase);

        _logger.LogInformation("Getting top {Count} teams from phase {Phase} for competition {CompetitionId}",
            advancingCount, currentPhase, competitionId);

        // Use stored procedure to get eligible teams efficiently
        var parameters = new
        {
            CompetitionId = competitionId,
            CurrentPhase = currentPhase,
            AdvancingCount = advancingCount
        };

        var eligibleTeams = await _competitionRepository.ExecuteStoredProcedureAsync<CompetitionTeam>("SP_GetEligibleTeamsForNextPhase", parameters);
        var result = eligibleTeams.Select(MapToCompetitionTeamDto).ToList();

        _logger.LogInformation("Returning {Count} eligible teams for advancement from {Phase}",
            result.Count, currentPhase);

        return result;
    }

    public async Task EliminateTeamsAsync(Guid competitionId, List<Guid> teamIds)
    {
        _logger.LogInformation("Eliminating {Count} teams from competition {CompetitionId}", teamIds.Count, competitionId);
        
        foreach (var teamId in teamIds)
        {
            var team = await _competitionTeamRepository.GetByIdAsync(teamId);
            if (team != null)
            {
                team.IsEliminated = true;
                team.PhaseEliminatedAt = DateTime.UtcNow;
                await _competitionTeamRepository.UpdateAsync(team);
            }
        }
    }

    public async Task AdvanceTeamsToNextPhaseAsync(Guid competitionId, List<Guid> teamIds)
    {
        _logger.LogInformation("Advancing {Count} teams to next phase in competition {CompetitionId}", teamIds.Count, competitionId);
        
        var competition = await _competitionRepository.GetByIdAsync(competitionId);
        if (competition == null)
            throw new ArgumentException("Competition not found");

        var nextPhase = GetNextPhase(competition.Phase);
        
        foreach (var teamId in teamIds)
        {
            var team = await _competitionTeamRepository.GetByIdAsync(teamId);
            if (team != null)
            {
                team.Phase = nextPhase;
                team.AdvancedToNextPhase = true;
                await _competitionTeamRepository.UpdateAsync(team);
            }
        }
    }

    private string GetNextPhase(string currentPhase)
    {
        return currentPhase switch
        {
            "Leaderboard" => "Top32",
            "Top32" => "Top16",
            "Top16" => "Top8",
            "Top8" => "Top4",
            "Top4" => "Final",
            "Final" => "Completed",
            _ => currentPhase
        };
    }

    private string GetPreviousPhase(string currentPhase)
    {
        return currentPhase switch
        {
            "Top32" => "Leaderboard",
            "Top16" => "Top32",
            "Top8" => "Top16",
            "Top4" => "Top8",
            "Final" => "Top4",
            "Completed" => "Final",
            _ => "Leaderboard"
        };
    }

    private DateTime? GetPhaseEndDate(string phase)
    {
        return phase switch
        {
            "Top32" => DateTime.UtcNow.AddDays(7), // 1 week for Top32 leaderboard phase
            "Top16" => DateTime.UtcNow.AddDays(3), // 3 days for knockout phases
            "Top8" => DateTime.UtcNow.AddDays(2),
            "Top4" => DateTime.UtcNow.AddDays(1),
            "Final" => DateTime.UtcNow.AddHours(12),
            _ => null
        };
    }

    private long? GetMaxGamesForPhase(string phase)
    {
        return phase switch
        {
            "Top32" => 10, // Max 10 games in Top32 phase
            "Top16" => 1,  // Single knockout game
            "Top8" => 1,   // Single knockout game
            "Top4" => 1,   // Single knockout game
            "Final" => 1,  // Single knockout game
            _ => null
        };
    }

    private bool IsKnockoutPhase(string phase)
    {
        return phase is "Top32" or "Top16" or "Top8" or "Top4" or "Final";
    }

    // Knockout Lobby Management Implementation
    public async Task<CompetitionPhaseLobbyDto> CreatePhaseLobbyAsync(CreateCompetitionPhaseLobbyDto createDto, Guid adminId)
    {
        _logger.LogInformation("Creating phase lobby for competition {CompetitionId} phase {Phase}",
            createDto.CompetitionId, createDto.Phase);

        if (createDto.TeamIds.Count != 2)
            throw new ArgumentException("Exactly 2 teams are required for a knockout lobby");

        try
        {
            var parameters = new
            {
                CompetitionId = createDto.CompetitionId,
                Phase = createDto.Phase,
                Team1Id = createDto.TeamIds[0],
                Team2Id = createDto.TeamIds[1],
                AdminId = adminId,
                MatchScheduledAt = createDto.MatchScheduledAt,
                BestOfGames = createDto.BestOfGames,
                RequiredWins = createDto.RequiredWins,
                LobbyId = Guid.Empty, // Output parameter
                LobbyCode = string.Empty // Output parameter
            };

            var result = await _competitionRepository.ExecuteStoredProcedureWithResultAsync<dynamic>(
                StoredProcedures.SP_CreatePhaseLobby, parameters);

            if (result != null)
            {
                return new CompetitionPhaseLobbyDto
                {
                    Id = result.Id,
                    CompetitionId = result.CompetitionId,
                    Phase = result.Phase,
                    LobbyCode = result.LobbyCode,
                    MatchScheduledAt = result.MatchScheduledAt,
                    BestOfGames = result.BestOfGames,
                    RequiredWins = result.RequiredWins,
                    MatchStatus = result.MatchStatus,
                    CreatedByAdminId = result.CreatedByAdminId,
                    CreatedByAdminUsername = result.CreatedByAdmin ?? "Admin",
                    CreatedAt = result.CreatedAt,
                    Teams = new List<CompetitionPhaseLobbyTeamDto>
                    {
                        new CompetitionPhaseLobbyTeamDto
                        {
                            TeamName = result.Team1Name,
                            Player1Name = result.Team1Player1,
                            Player2Name = result.Team1Player2
                        },
                        new CompetitionPhaseLobbyTeamDto
                        {
                            TeamName = result.Team2Name,
                            Player1Name = result.Team2Player1,
                            Player2Name = result.Team2Player2
                        }
                    }
                };
            }

            throw new InvalidOperationException("Failed to create phase lobby");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating phase lobby for competition {CompetitionId}", createDto.CompetitionId);
            throw;
        }
    }

    public async Task<List<CompetitionPhaseLobbyDto>> GetPhaseLobbiesAsync(Guid competitionId, string phase)
    {
        _logger.LogInformation("Getting phase lobbies for competition {CompetitionId} phase {Phase}",
            competitionId, phase);

        try
        {
            // Leaderboard phase doesn't have lobbies
            if (phase == "Leaderboard")
            {
                _logger.LogInformation("Leaderboard phase has no lobbies for competition {CompetitionId}", competitionId);
                return new List<CompetitionPhaseLobbyDto>();
            }

            // For knockout phases, this would need proper implementation
            // For now, return empty list
            _logger.LogWarning("Phase lobby retrieval not implemented for phase {Phase}", phase);
            return new List<CompetitionPhaseLobbyDto>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting phase lobbies for competition {CompetitionId}", competitionId);
            throw;
        }
    }

    public async Task<CompetitionPhaseLobbyDto> GetPhaseLobbyByCodeAsync(string lobbyCode)
    {
        _logger.LogInformation("Getting phase lobby by code {LobbyCode}", lobbyCode);

        try
        {
            var parameters = new { LobbyCode = lobbyCode };
            var results = await _competitionRepository.ExecuteStoredProcedureWithMultipleResultSetsAsync(
                StoredProcedures.SP_GetPhaseLobbyDetails, parameters);

            if (results?.Count >= 2)
            {
                var lobbyData = results[0].FirstOrDefault();
                var teamsData = results[1];
                var gamesData = results.Count > 2 ? results[2] : new List<dynamic>();

                if (lobbyData != null)
                {
                    return new CompetitionPhaseLobbyDto
                    {
                        Id = lobbyData.Id,
                        CompetitionId = lobbyData.CompetitionId,
                        Phase = lobbyData.Phase,
                        LobbyCode = lobbyData.LobbyCode,
                        MatchScheduledAt = lobbyData.MatchScheduledAt,
                        BestOfGames = lobbyData.BestOfGames,
                        RequiredWins = lobbyData.RequiredWins,
                        MatchStatus = lobbyData.MatchStatus,
                        CreatedByAdminUsername = lobbyData.CreatedByAdmin,
                        CreatedAt = lobbyData.CreatedAt,
                        Teams = teamsData?.Select(t => new CompetitionPhaseLobbyTeamDto
                        {
                            Id = t.Id,
                            LobbyId = t.LobbyId,
                            CompetitionTeamId = t.CompetitionTeamId,
                            TeamName = t.TeamName,
                            IsWinner = t.IsWinner,
                            EliminatedAt = t.EliminatedAt,
                            Player1Id = t.Player1Id,
                            Player2Id = t.Player2Id,
                            Player1Name = t.Player1Name,
                            Player2Name = t.Player2Name
                        }).ToList() ?? new List<CompetitionPhaseLobbyTeamDto>(),
                        Games = gamesData?.Select(g => new GameSummaryDto
                        {
                            Id = g.Id,
                            LobbyCode = g.LobbyCode,
                            Status = g.Status,
                            WinnerTeam = g.WinnerTeam,
                            WinnerTeamName = g.WinnerTeamName,
                            Team1Score = g.Team1Score,
                            Team2Score = g.Team2Score,
                            Team1BallsWon = g.Team1BallsWon,
                            Team2BallsWon = g.Team2BallsWon,
                            StartedAt = g.StartedAt,
                            CompletedAt = g.CompletedAt
                        }).ToList() ?? new List<GameSummaryDto>()
                    };
                }
            }

            throw new ArgumentException($"Phase lobby with code {lobbyCode} not found");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting phase lobby by code {LobbyCode}", lobbyCode);
            throw;
        }
    }

    public async Task<CompetitionPhaseLobbyDto> GetPhaseLobbyByIdAsync(Guid lobbyId)
    {
        _logger.LogInformation("Getting phase lobby by ID {LobbyId}", lobbyId);

        try
        {
            var parameters = new { LobbyId = lobbyId };
            var results = await _competitionRepository.ExecuteStoredProcedureWithMultipleResultSetsAsync(
                StoredProcedures.SP_GetPhaseLobbyDetails, parameters);

            if (results?.Count >= 2)
            {
                var lobbyData = results[0].FirstOrDefault();
                var teamsData = results[1];
                var gamesData = results.Count > 2 ? results[2] : new List<dynamic>();

                if (lobbyData != null)
                {
                    return new CompetitionPhaseLobbyDto
                    {
                        Id = lobbyData.Id,
                        CompetitionId = lobbyData.CompetitionId,
                        Phase = lobbyData.Phase,
                        LobbyCode = lobbyData.LobbyCode,
                        MatchScheduledAt = lobbyData.MatchScheduledAt,
                        BestOfGames = lobbyData.BestOfGames,
                        RequiredWins = lobbyData.RequiredWins,
                        MatchStatus = lobbyData.MatchStatus,
                        CreatedByAdminUsername = lobbyData.CreatedByAdmin,
                        CreatedAt = lobbyData.CreatedAt,
                        Teams = teamsData?.Select(t => new CompetitionPhaseLobbyTeamDto
                        {
                            Id = t.Id,
                            LobbyId = t.LobbyId,
                            CompetitionTeamId = t.CompetitionTeamId,
                            TeamName = t.TeamName,
                            IsWinner = t.IsWinner,
                            EliminatedAt = t.EliminatedAt,
                            Player1Id = t.Player1Id,
                            Player2Id = t.Player2Id,
                            Player1Name = t.Player1Name,
                            Player2Name = t.Player2Name
                        }).ToList() ?? new List<CompetitionPhaseLobbyTeamDto>(),
                        Games = gamesData?.Select(g => new GameSummaryDto
                        {
                            Id = g.Id,
                            LobbyCode = g.LobbyCode,
                            Status = g.Status,
                            WinnerTeam = g.WinnerTeam,
                            WinnerTeamName = g.WinnerTeamName,
                            Team1Score = g.Team1Score,
                            Team2Score = g.Team2Score,
                            Team1BallsWon = g.Team1BallsWon,
                            Team2BallsWon = g.Team2BallsWon,
                            StartedAt = g.StartedAt,
                            CompletedAt = g.CompletedAt
                        }).ToList() ?? new List<GameSummaryDto>()
                    };
                }
            }

            throw new ArgumentException($"Phase lobby with ID {lobbyId} not found");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting phase lobby by ID {LobbyId}", lobbyId);
            throw;
        }
    }

    public async Task DeletePhaseLobbyAsync(Guid lobbyId)
    {
        _logger.LogInformation("Deleting phase lobby {LobbyId}", lobbyId);

        try
        {
            // Implementation would delete the lobby and associated teams
            // For now, we'll throw not implemented as this requires careful handling
            throw new NotImplementedException("Delete phase lobby requires careful implementation to handle associated data");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting phase lobby {LobbyId}", lobbyId);
            throw;
        }
    }

    public async Task SetLobbyWinnerAsync(Guid lobbyId, Guid winnerTeamId)
    {
        _logger.LogInformation("Setting lobby winner for lobby {LobbyId}, winner team {WinnerTeamId}",
            lobbyId, winnerTeamId);

        try
        {
            // This would typically be handled automatically by ProcessGameResult
            // Manual override might be needed for exceptional cases
            throw new NotImplementedException("Manual winner setting requires additional validation logic");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting lobby winner for lobby {LobbyId}", lobbyId);
            throw;
        }
    }

    // Match Management Implementation
    public async Task ScheduleMatchAsync(Guid lobbyId, DateTime scheduledAt, string? customMessage = null)
    {
        _logger.LogInformation("Scheduling match for lobby {LobbyId} at {ScheduledAt}", lobbyId, scheduledAt);

        try
        {
            // Update lobby with scheduled time
            var parameters = new
            {
                LobbyId = lobbyId,
                MatchScheduledAt = scheduledAt,
                MatchStatus = "Scheduled"
            };

            await _competitionRepository.ExecuteStoredProcedureAsync(
                "UPDATE CompetitionPhaseLobbies SET MatchScheduledAt = @MatchScheduledAt, MatchStatus = @MatchStatus WHERE Id = @LobbyId",
                parameters);

            // Send notification if requested
            await SendMatchNotificationAsync(lobbyId, "MatchScheduled");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error scheduling match for lobby {LobbyId}", lobbyId);
            throw;
        }
    }

    public async Task SendMatchNotificationAsync(Guid lobbyId, string notificationType = "MatchScheduled")
    {
        _logger.LogInformation("Sending match notification for lobby {LobbyId}, type {NotificationType}",
            lobbyId, notificationType);

        try
        {
            // Get lobby details for notification
            var lobby = await GetPhaseLobbyByIdAsync(lobbyId);

            // Create notification record
            var parameters = new
            {
                Id = Guid.NewGuid(),
                LobbyId = lobbyId,
                NotificationType = notificationType,
                Status = "Sent",
                EmailContent = $"Match notification for {lobby.Phase} phase"
            };

            await _competitionRepository.ExecuteStoredProcedureAsync(
                "INSERT INTO MatchNotifications (Id, LobbyId, NotificationType, Status, EmailContent, SentAt) VALUES (@Id, @LobbyId, @NotificationType, @Status, @EmailContent, GETUTCDATE())",
                parameters);

            // Update lobby notification timestamp
            await _competitionRepository.ExecuteStoredProcedureAsync(
                "UPDATE CompetitionPhaseLobbies SET NotificationSentAt = GETUTCDATE() WHERE Id = @LobbyId",
                new { LobbyId = lobbyId });

            _logger.LogInformation("Match notification sent successfully for lobby {LobbyId}", lobbyId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending match notification for lobby {LobbyId}", lobbyId);
            throw;
        }
    }

    public async Task<MatchResultDto> ProcessGameResultAsync(Guid gameId, Guid phaseLobbyId)
    {
        _logger.LogInformation("Processing game result for game {GameId} in lobby {PhaseLobbyId}",
            gameId, phaseLobbyId);

        try
        {
            var parameters = new
            {
                GameId = gameId,
                PhaseLobbyId = phaseLobbyId
            };

            var result = await _competitionRepository.ExecuteStoredProcedureWithResultAsync<dynamic>(
                StoredProcedures.SP_ProcessGameResult, parameters);

            if (result != null)
            {
                var matchResult = result.FirstOrDefault();
                if (matchResult != null)
                {
                    return new MatchResultDto
                    {
                        LobbyId = phaseLobbyId,
                        IsMatchComplete = matchResult.IsMatchComplete,
                        Team1Wins = matchResult.Team1Wins,
                        Team2Wins = matchResult.Team2Wins,
                        RequiredWins = matchResult.RequiredWins,
                        WinnerTeamNumber = matchResult.WinnerTeamNumber,
                        WinnerTeamId = matchResult.WinnerTeamId,
                        TotalGames = matchResult.Team1Wins + matchResult.Team2Wins
                    };
                }
            }

            throw new InvalidOperationException("Failed to process game result");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing game result for game {GameId}", gameId);
            throw;
        }
    }

    public async Task<MatchResultDto> EvaluateMatchResultAsync(Guid lobbyId)
    {
        _logger.LogInformation("Evaluating match result for lobby {LobbyId}", lobbyId);

        try
        {
            var lobby = await GetPhaseLobbyByIdAsync(lobbyId);
            var team1Wins = lobby.Games.Count(g => g.WinnerTeam == 1);
            var team2Wins = lobby.Games.Count(g => g.WinnerTeam == 2);

            var isComplete = team1Wins >= lobby.RequiredWins || team2Wins >= lobby.RequiredWins;
            var winnerTeamNumber = team1Wins >= lobby.RequiredWins ? 1 :
                                 team2Wins >= lobby.RequiredWins ? 2 : (int?)null;

            return new MatchResultDto
            {
                LobbyId = lobbyId,
                IsMatchComplete = isComplete,
                Team1Wins = team1Wins,
                Team2Wins = team2Wins,
                RequiredWins = lobby.RequiredWins,
                WinnerTeamNumber = winnerTeamNumber,
                TotalGames = lobby.Games.Count
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error evaluating match result for lobby {LobbyId}", lobbyId);
            throw;
        }
    }

    public async Task<bool> IsMatchCompleteAsync(Guid lobbyId)
    {
        _logger.LogInformation("Checking if match is complete for lobby {LobbyId}", lobbyId);

        try
        {
            var result = await EvaluateMatchResultAsync(lobbyId);
            return result.IsMatchComplete;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking match completion for lobby {LobbyId}", lobbyId);
            throw;
        }
    }

    public async Task<BracketDto> GenerateBracketAsync(Guid competitionId, string phase)
    {
        throw new NotImplementedException("GenerateBracketAsync requires full implementation");
    }

    public async Task<List<BracketDto>> GetCompetitionBracketsAsync(Guid competitionId)
    {
        throw new NotImplementedException("GetCompetitionBracketsAsync requires full implementation");
    }

    public async Task<CompetitionTeamPhaseStatsDto> CreatePhaseStatsAsync(Guid competitionTeamId, string phase)
    {
        throw new NotImplementedException("CreatePhaseStatsAsync requires full implementation");
    }

    public async Task<CompetitionTeamPhaseStatsDto> UpdatePhaseStatsAsync(Guid competitionTeamId, string phase, int points, int bonusPoints, int ballsWon)
    {
        throw new NotImplementedException("UpdatePhaseStatsAsync requires full implementation");
    }

    public async Task<List<CompetitionTeamPhaseStatsDto>> GetTeamPhaseStatsAsync(Guid competitionTeamId)
    {
        throw new NotImplementedException("GetTeamPhaseStatsAsync requires full implementation");
    }

    public async Task ProcessPhaseEndAsync(Guid competitionId)
    {
        throw new NotImplementedException("ProcessPhaseEndAsync requires full implementation");
    }

    public async Task<bool> CanAdvanceToNextPhaseAsync(Guid competitionId)
    {
        throw new NotImplementedException("CanAdvanceToNextPhaseAsync requires full implementation");
    }

    public async Task<List<CompetitionTeamDto>> CalculatePhaseRankingsAsync(Guid competitionId, string phase)
    {
        throw new NotImplementedException("CalculatePhaseRankingsAsync requires full implementation");
    }

    private CompetitionDto MapToCompetitionDto(Competition competition)
    {
        return new CompetitionDto
        {
            Id = competition.Id,
            Name = competition.Name,
            Description = competition.Description,
            StartDate = competition.StartDate,
            EndDate = competition.EndDate,
            Status = competition.Status,
            MaxTeams = competition.MaxTeams,
            EntryFee = competition.EntryFee,
            Phase = competition.Phase,
            PhaseEndDate = competition.PhaseEndDate,
            CreatedAt = competition.CreatedAt
        };
    }

    private async Task<CompetitionTeamDto> MapToCompetitionTeamDtoAsync(CompetitionTeam team)
    {
        var player1 = await _userRepository.GetByIdAsync(team.Player1Id);
        var player2 = team.Player2Id.HasValue ? await _userRepository.GetByIdAsync(team.Player2Id.Value) : null;

        return new CompetitionTeamDto
        {
            Id = team.Id,
            TeamName = team.TeamName,
            Player1 = player1 != null ? new PlayerDto { Id = player1.Id, Username = player1.Username } : new PlayerDto(),
            Player2 = player2 != null ? new PlayerDto { Id = player2.Id, Username = player2.Username } : null,
            InviteCode = team.InviteCode,
            GamesPlayed = team.GamesPlayed,
            Points = team.Points,
            BonusPoints = team.BonusPoints,
            MaxGames = team.MaxGames,
            IsComplete = team.IsComplete,
            RegisteredAt = team.RegisteredAt,
            CompletedAt = team.CompletedAt,
            Phase = team.Phase,
            IsEliminated = team.IsEliminated,
            AdvancedToNextPhase = team.AdvancedToNextPhase,
            PhaseEliminatedAt = team.PhaseEliminatedAt
        };
    }

    private CompetitionTeamDto MapToCompetitionTeamDto(CompetitionTeam team)
    {
        return new CompetitionTeamDto
        {
            Id = team.Id,
            TeamName = team.TeamName,
            InviteCode = team.InviteCode,
            GamesPlayed = team.GamesPlayed,
            Points = team.Points,
            BonusPoints = team.BonusPoints,
            MaxGames = team.MaxGames,
            IsComplete = team.IsComplete,
            RegisteredAt = team.RegisteredAt,
            CompletedAt = team.CompletedAt,
            Phase = team.Phase,
            IsEliminated = team.IsEliminated,
            AdvancedToNextPhase = team.AdvancedToNextPhase,
            PhaseEliminatedAt = team.PhaseEliminatedAt
        };
    }

    private int GetAdvancingCountForPhase(string currentPhase)
    {
        return currentPhase switch
        {
            "Leaderboard" => 32,  // Top 32 teams advance to Top32
            "Top32" => 16,        // Top 16 teams advance to Top16
            "Top16" => 8,         // Top 8 teams advance to Top8
            "Top8" => 4,          // Top 4 teams advance to Top4
            "Top4" => 2,          // Top 2 teams advance to Final
            "Final" => 1,         // Winner
            _ => 0
        };
    }

    // Admin Management Implementation
    public async Task<List<AdminLobbyViewDto>> GetAdminLobbyViewAsync(Guid competitionId, string? phase = null)
    {
        _logger.LogInformation("Getting admin lobby view for competition {CompetitionId} phase {Phase}",
            competitionId, phase);

        try
        {
            var parameters = new { CompetitionId = competitionId, Phase = phase };
            var results = await _competitionRepository.ExecuteStoredProcedureWithResultAsync<dynamic>(
                StoredProcedures.SP_GetAdminLobbyView, parameters);

            return results?.Select(r => new AdminLobbyViewDto
            {
                LobbyId = r.LobbyId,
                LobbyCode = r.LobbyCode,
                Phase = r.Phase,
                MatchStatus = r.MatchStatus,
                MatchScheduledAt = r.MatchScheduledAt,
                BestOfGames = r.BestOfGames,
                RequiredWins = r.RequiredWins,
                CompletedAt = r.CompletedAt,
                NotificationSentAt = r.NotificationSentAt,
                CreatedByAdmin = r.CreatedByAdmin,
                Team1Id = r.Team1Id,
                Team1Name = r.Team1Name,
                Team1Player1 = r.Team1Player1,
                Team1Player2 = r.Team1Player2,
                Team2Id = r.Team2Id,
                Team2Name = r.Team2Name,
                Team2Player1 = r.Team2Player1,
                Team2Player2 = r.Team2Player2,
                GamesPlayed = r.GamesPlayed,
                Team1Wins = r.Team1Wins,
                Team2Wins = r.Team2Wins,
                WinnerTeamName = r.WinnerTeamName,
                WinnerTeamId = r.WinnerTeamId
            }).ToList() ?? new List<AdminLobbyViewDto>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting admin lobby view for competition {CompetitionId}", competitionId);
            throw;
        }
    }

    public async Task<List<CompetitionTeamDto>> GetPhaseWinnersAsync(Guid competitionId, string phase)
    {
        _logger.LogInformation("Getting phase winners for competition {CompetitionId} phase {Phase}",
            competitionId, phase);

        try
        {
            var parameters = new { CompetitionId = competitionId, Phase = phase };
            var results = await _competitionRepository.ExecuteStoredProcedureWithResultAsync<dynamic>(
                StoredProcedures.SP_GetPhaseWinners, parameters);

            return results?.Select(r => new CompetitionTeamDto
            {
                Id = r.Id,
                TeamName = r.TeamName,
                Player1 = new PlayerDto { Id = r.Player1Id, Username = r.Player1Name },
                Player2 = r.Player2Id != null ? new PlayerDto { Id = r.Player2Id, Username = r.Player2Name } : null,
                Phase = r.Phase
            }).ToList() ?? new List<CompetitionTeamDto>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting phase winners for competition {CompetitionId}", competitionId);
            throw;
        }
    }

    public async Task AdvancePhaseAsync(Guid competitionId, string currentPhase, string nextPhase)
    {
        _logger.LogInformation("Advancing phase for competition {CompetitionId} from {CurrentPhase} to {NextPhase}",
            competitionId, currentPhase, nextPhase);

        try
        {
            // This method is for knockout phases only
            // For Leaderboard phase, use AdvanceCompetitionPhaseAsync instead
            throw new NotImplementedException($"Knockout phase advancement from {currentPhase} to {nextPhase} is not yet implemented");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error advancing phase for competition {CompetitionId}", competitionId);
            throw;
        }
    }

    public async Task<bool> CanAdvancePhaseAsync(Guid competitionId, string phase)
    {
        _logger.LogInformation("Checking if phase can be advanced for competition {CompetitionId} phase {Phase}",
            competitionId, phase);

        try
        {
            // For Leaderboard phase, we can always advance (no matches to complete)
            if (phase == "Leaderboard")
            {
                _logger.LogInformation("Leaderboard phase can always be advanced for competition {CompetitionId}", competitionId);
                return true;
            }

            // For knockout phases, check if all matches in the phase are completed
            var lobbies = await GetPhaseLobbiesAsync(competitionId, phase);
            var incompleteMatches = lobbies.Where(l => l.MatchStatus != "Completed").ToList();

            var canAdvance = !incompleteMatches.Any();

            _logger.LogInformation("Phase advancement check for competition {CompetitionId} phase {Phase}: {CanAdvance} ({IncompleteCount} incomplete matches)",
                competitionId, phase, canAdvance, incompleteMatches.Count);

            return canAdvance;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking phase advancement for competition {CompetitionId}", competitionId);
            throw;
        }
    }
}
