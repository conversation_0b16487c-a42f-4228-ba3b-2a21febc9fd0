using Microsoft.Extensions.Logging;
using ThuneeAPI.Application.DTOs;
using ThuneeAPI.Application.Interfaces;
using ThuneeAPI.Core.Entities;
using ThuneeAPI.Infrastructure.Data.Repositories;

namespace ThuneeAPI.Infrastructure.Services;

// Helper class for stored procedure results
public class CompetitionTeamWithUserInfo
{
    public Guid Id { get; set; }
    public string TeamName { get; set; } = string.Empty;
    public Guid Player1Id { get; set; }
    public Guid? Player2Id { get; set; }
    public string InviteCode { get; set; } = string.Empty;
    public int GamesPlayed { get; set; }
    public int Points { get; set; }
    public int BonusPoints { get; set; }
    public int TotalPoints { get; set; }
    public int MaxGames { get; set; }
    public bool IsActive { get; set; }
    public bool IsComplete { get; set; }
    public DateTime RegisteredAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public string Phase { get; set; } = string.Empty;
    public bool IsEliminated { get; set; }
    public bool AdvancedToNextPhase { get; set; }
    public DateTime? PhaseEliminatedAt { get; set; }
    public string? Player1Username { get; set; }
    public string? Player2Username { get; set; }
}

// Static class for SQL command names
public static class SqlCommands
{
    public const string SP_AdvanceCompetitionPhase = "SP_AdvanceCompetitionPhase";
    public const string SP_GetEligibleTeamsForPhase = "SP_GetEligibleTeamsForPhase";
    public const string SP_GetTeamsByPhase = "SP_GetTeamsByPhase";
    public const string SP_CheckTeamEligibilityForGames = "SP_CheckTeamEligibilityForGames";
    public const string SP_CreateCompetitionPhaseLobby = "SP_CreateCompetitionPhaseLobby";
    public const string SP_SetPhaseLobbyWinner = "SP_SetPhaseLobbyWinner";
}

// Stored Procedures class for knockout competition
public static class StoredProcedures
{
    public const string SP_CreatePhaseLobby = "SP_CreatePhaseLobby";
    public const string SP_GetPhaseLobbyDetails = "SP_GetPhaseLobbyDetails";
    public const string SP_ProcessGameResult = "SP_ProcessGameResult";
    public const string SP_GetPhaseLobbies = "SP_GetPhaseLobbies";
    public const string SP_GetPhaseWinners = "SP_GetPhaseWinners";
    public const string SP_AdvancePhase = "SP_AdvancePhase";
    public const string SP_GetAdminLobbyView = "SP_GetAdminLobbyView";
}

public class CompetitionPhaseService : ICompetitionPhaseService
{
    private readonly ILogger<CompetitionPhaseService> _logger;
    private readonly ICompetitionRepository _competitionRepository;
    private readonly ICompetitionTeamRepository _competitionTeamRepository;
    private readonly IUserRepository _userRepository;
    private readonly IEmailService _emailService;

    public CompetitionPhaseService(
        ILogger<CompetitionPhaseService> logger,
        ICompetitionRepository competitionRepository,
        ICompetitionTeamRepository competitionTeamRepository,
        IUserRepository userRepository,
        IEmailService emailService)
    {
        _logger = logger;
        _competitionRepository = competitionRepository;
        _competitionTeamRepository = competitionTeamRepository;
        _userRepository = userRepository;
        _emailService = emailService;
    }

    public async Task<CompetitionDto> AdvanceCompetitionPhaseAsync(Guid competitionId, string newPhase)
    {
        _logger.LogInformation("Advancing competition {CompetitionId} to phase {Phase}", competitionId, newPhase);

        var competition = await _competitionRepository.GetByIdAsync(competitionId);
        if (competition == null)
            throw new ArgumentException("Competition not found");

        try
        {
            // Use the comprehensive stored procedure that handles everything
            var spParameters = new
            {
                CompetitionId = competitionId,
                NewPhase = newPhase,
                PhaseEndDate = GetPhaseEndDate(newPhase),
                MaxGamesPerPhase = GetMaxGamesForPhase(newPhase)
            };

            _logger.LogInformation("Executing SP_AdvanceCompetitionToTop32 for competition {CompetitionId} to phase {Phase}",
                competitionId, newPhase);

            Console.WriteLine($"[DEBUG] About to execute SP_AdvanceCompetitionToTop32 with parameters: CompetitionId={competitionId}, NewPhase={newPhase}, PhaseEndDate={spParameters.PhaseEndDate}, MaxGamesPerPhase={spParameters.MaxGamesPerPhase}");

            // Execute the comprehensive stored procedure and get results
            var spResults = await _competitionRepository.ExecuteStoredProcedureAsync<dynamic>("SP_AdvanceCompetitionToTop32", spParameters);

            Console.WriteLine($"[DEBUG] SP_AdvanceCompetitionToTop32 executed. Results count: {spResults?.Count() ?? 0}");

            // Check if we got results from the stored procedure
            if (spResults != null && spResults.Any())
            {
                var firstResult = spResults.First();
                Console.WriteLine($"[DEBUG] SP Result: {firstResult}");

                // Check if the stored procedure reported success
                if (firstResult.Result == "Success")
                {
                    Console.WriteLine($"[DEBUG] SP Success: Advanced {firstResult.TeamsAdvanced} teams, eliminated {firstResult.TeamsEliminated} teams");
                    _logger.LogInformation("SP_AdvanceCompetitionToTop32 succeeded: Advanced {TeamsAdvanced} teams, eliminated {TeamsEliminated} teams",
                        (int)firstResult.TeamsAdvanced, (int)firstResult.TeamsEliminated);
                }
                else
                {
                    Console.WriteLine($"[DEBUG] SP Error: {firstResult.ErrorMessage}");
                    _logger.LogError("SP_AdvanceCompetitionToTop32 failed: {ErrorMessage}", (string)firstResult.ErrorMessage);
                    throw new InvalidOperationException($"Stored procedure failed: {firstResult.ErrorMessage}");
                }
            }
            else
            {
                Console.WriteLine($"[DEBUG] No results returned from SP_AdvanceCompetitionToTop32");
                _logger.LogWarning("SP_AdvanceCompetitionToTop32 returned no results");
            }

            // Refresh the competition data from database to get updated values
            var updatedCompetition = await _competitionRepository.GetByIdAsync(competitionId);
            if (updatedCompetition == null)
                throw new InvalidOperationException("Competition not found after update");

            _logger.LogInformation("Successfully advanced competition {CompetitionId} to phase {Phase}",
                competitionId, newPhase);

            return MapToCompetitionDto(updatedCompetition);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error advancing competition {CompetitionId} to phase {Phase}", competitionId, newPhase);
            throw;
        }
    }

    public async Task<List<CompetitionTeamDto>> GetTeamsForPhaseAsync(Guid competitionId, string phase)
    {
        _logger.LogInformation("Getting teams for competition {CompetitionId} phase {Phase}", competitionId, phase);

        try
        {
            var teams = await _competitionTeamRepository.GetByCompetitionIdAsync(competitionId);
            var phaseTeams = teams.Where(t => t.Phase == phase && !t.IsEliminated).ToList();

            var teamDtos = new List<CompetitionTeamDto>();
            foreach (var team in phaseTeams)
            {
                var teamDto = await MapToCompetitionTeamDtoAsync(team);
                teamDtos.Add(teamDto);
            }

            _logger.LogInformation("Found {Count} teams in phase {Phase} for competition {CompetitionId}",
                teamDtos.Count, phase, competitionId);

            return teamDtos;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting teams for phase {Phase} in competition {CompetitionId}", phase, competitionId);
            throw;
        }
    }

    public async Task<List<CompetitionTeamDto>> GetEligibleTeamsForNextPhaseAsync(Guid competitionId)
    {
        _logger.LogInformation("Getting eligible teams for next phase in competition {CompetitionId}", competitionId);

        var competition = await _competitionRepository.GetByIdAsync(competitionId);
        if (competition == null)
            throw new ArgumentException("Competition not found");

        var currentPhase = competition.Phase ?? "Leaderboard";
        var advancingCount = GetAdvancingCountForPhase(currentPhase);

        _logger.LogInformation("Getting top {Count} teams from phase {Phase} for competition {CompetitionId}",
            advancingCount, currentPhase, competitionId);

        // Get all teams in the current phase and sort them by total points
        var allTeams = await _competitionTeamRepository.GetByCompetitionIdAsync(competitionId);
        var currentPhaseTeams = allTeams.Where(t => t.Phase == currentPhase && !t.IsEliminated).ToList();

        // Sort teams by total points (points + bonus points) descending, same logic as SQL script
        var sortedTeams = currentPhaseTeams
            .OrderByDescending(t => t.Points + t.BonusPoints)
            .ThenByDescending(t => t.Points)
            .ThenBy(t => t.GamesPlayed)
            .ThenBy(t => t.RegisteredAt)
            .Take(advancingCount)
            .ToList();

        var result = new List<CompetitionTeamDto>();
        foreach (var team in sortedTeams)
        {
            var teamDto = await MapToCompetitionTeamDtoAsync(team);
            result.Add(teamDto);
        }

        _logger.LogInformation("Returning {Count} eligible teams for advancement from {Phase} (out of {TotalTeams} teams)",
            result.Count, currentPhase, currentPhaseTeams.Count);

        return result;
    }

    public async Task EliminateTeamsAsync(Guid competitionId, List<Guid> teamIds)
    {
        _logger.LogInformation("Eliminating {Count} teams from competition {CompetitionId}", teamIds.Count, competitionId);
        
        foreach (var teamId in teamIds)
        {
            var team = await _competitionTeamRepository.GetByIdAsync(teamId);
            if (team != null)
            {
                team.IsEliminated = true;
                team.PhaseEliminatedAt = DateTime.UtcNow;
                await _competitionTeamRepository.UpdateAsync(team);
            }
        }
    }

    public async Task AdvanceTeamsToNextPhaseAsync(Guid competitionId, List<Guid> teamIds)
    {
        _logger.LogInformation("Advancing {Count} teams to next phase in competition {CompetitionId}", teamIds.Count, competitionId);
        
        var competition = await _competitionRepository.GetByIdAsync(competitionId);
        if (competition == null)
            throw new ArgumentException("Competition not found");

        var nextPhase = GetNextPhase(competition.Phase);
        
        foreach (var teamId in teamIds)
        {
            var team = await _competitionTeamRepository.GetByIdAsync(teamId);
            if (team != null)
            {
                team.Phase = nextPhase;
                team.AdvancedToNextPhase = true;
                await _competitionTeamRepository.UpdateAsync(team);
            }
        }
    }

    private string GetNextPhase(string currentPhase)
    {
        return currentPhase switch
        {
            "Leaderboard" => "Top32",
            "Top32" => "Top16",
            "Top16" => "Top8",
            "Top8" => "Top4",
            "Top4" => "Final",
            "Final" => "Completed",
            _ => currentPhase
        };
    }

    private string GetPreviousPhase(string currentPhase)
    {
        return currentPhase switch
        {
            "Top32" => "Leaderboard",
            "Top16" => "Top32",
            "Top8" => "Top16",
            "Top4" => "Top8",
            "Final" => "Top4",
            "Completed" => "Final",
            _ => "Leaderboard"
        };
    }

    private DateTime? GetPhaseEndDate(string phase)
    {
        return phase switch
        {
            "Top32" => DateTime.UtcNow.AddDays(7), // 1 week for Top32 leaderboard phase
            "Top16" => DateTime.UtcNow.AddDays(3), // 3 days for knockout phases
            "Top8" => DateTime.UtcNow.AddDays(2),
            "Top4" => DateTime.UtcNow.AddDays(1),
            "Final" => DateTime.UtcNow.AddHours(12),
            _ => null
        };
    }

    private long? GetMaxGamesForPhase(string phase)
    {
        return phase switch
        {
            "Top32" => 10, // Max 10 games in Top32 phase
            "Top16" => 1,  // Single knockout game
            "Top8" => 1,   // Single knockout game
            "Top4" => 1,   // Single knockout game
            "Final" => 1,  // Single knockout game
            _ => null
        };
    }

    private bool IsKnockoutPhase(string phase)
    {
        return phase is "Top32" or "Top16" or "Top8" or "Top4" or "Final";
    }

    // Knockout Lobby Management Implementation
    public async Task<CompetitionPhaseLobbyDto> CreatePhaseLobbyAsync(CreateCompetitionPhaseLobbyDto createDto, Guid adminId)
    {
        _logger.LogInformation("Creating phase lobby for competition {CompetitionId} phase {Phase}",
            createDto.CompetitionId, createDto.Phase);

        if (createDto.TeamIds.Count != 2)
            throw new ArgumentException("Exactly 2 teams are required for a knockout lobby");

        try
        {
            var parameters = new
            {
                CompetitionId = createDto.CompetitionId,
                Phase = createDto.Phase,
                Team1Id = createDto.TeamIds[0],
                Team2Id = createDto.TeamIds[1],
                AdminId = adminId,
                MatchScheduledAt = createDto.MatchScheduledAt,
                BestOfGames = createDto.BestOfGames,
                RequiredWins = createDto.RequiredWins,
                LobbyId = Guid.Empty, // Output parameter
                LobbyCode = string.Empty // Output parameter
            };

            _logger.LogInformation("Executing SP_CreatePhaseLobby for teams {Team1Id} vs {Team2Id}",
                createDto.TeamIds[0], createDto.TeamIds[1]);

            // Execute the stored procedure
            var result = await _competitionRepository.ExecuteStoredProcedureAsync<dynamic>("SP_CreatePhaseLobby", parameters);

            if (result != null && result.Any())
            {
                var spResult = result.First();
                if (spResult.Result == "Success")
                {
                    // Get the created lobby details
                    var lobby = await GetPhaseLobbyByIdAsync((Guid)spResult.LobbyId);

                    _logger.LogInformation("Successfully created phase lobby {LobbyCode} for competition {CompetitionId}",
                        (string)spResult.LobbyCode, createDto.CompetitionId);

                    return lobby;
                }
                else
                {
                    throw new InvalidOperationException($"Failed to create phase lobby: {spResult.Result}");
                }
            }
            else
            {
                throw new InvalidOperationException("No result returned from SP_CreatePhaseLobby");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating phase lobby for competition {CompetitionId}", createDto.CompetitionId);
            throw;
        }
    }

    public async Task<List<CompetitionPhaseLobbyDto>> CreateAllPhaseMatchesAsync(Guid competitionId, string phase, Guid adminId, DateTime startTime, int matchIntervalMinutes = 60, int bestOfGames = 3)
    {
        _logger.LogInformation("Creating all matches for competition {CompetitionId} phase {Phase}", competitionId, phase);

        try
        {
            var parameters = new
            {
                CompetitionId = competitionId,
                Phase = phase,
                AdminId = adminId,
                StartTime = startTime,
                MatchIntervalMinutes = matchIntervalMinutes,
                BestOfGames = bestOfGames
            };

            _logger.LogInformation("Executing SP_CreateAllPhaseMatches for {Phase} starting at {StartTime}",
                phase, startTime);

            // Execute the stored procedure
            var result = await _competitionRepository.ExecuteStoredProcedureAsync<dynamic>("SP_CreateAllPhaseMatches", parameters);

            if (result != null && result.Any())
            {
                var spResult = result.First();
                _logger.LogInformation("Successfully created {MatchesCreated} matches for {TotalTeams} teams in phase {Phase}",
                    (int)spResult.MatchesCreated, (int)spResult.TotalTeams, phase);

                // Return the created lobbies
                return await GetPhaseLobbiesAsync(competitionId, phase);
            }
            else
            {
                throw new InvalidOperationException("No result returned from SP_CreateAllPhaseMatches");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating all phase matches for competition {CompetitionId} phase {Phase}", competitionId, phase);
            throw;
        }
    }

    public async Task<List<CompetitionPhaseLobbyDto>> GetPhaseLobbiesAsync(Guid competitionId, string phase)
    {
        _logger.LogInformation("Getting phase lobbies for competition {CompetitionId} phase {Phase}",
            competitionId, phase);

        // Leaderboard phase doesn't have lobbies
        if (phase == "Leaderboard")
        {
            _logger.LogInformation("Leaderboard phase has no lobbies for competition {CompetitionId}", competitionId);
            return new List<CompetitionPhaseLobbyDto>();
        }

        // For knockout phases, this would need proper implementation
        // For now, return empty list
        _logger.LogWarning("Phase lobby retrieval not implemented for phase {Phase}", phase);
        return new List<CompetitionPhaseLobbyDto>();
    }

    public async Task<CompetitionPhaseLobbyDto> GetPhaseLobbyByCodeAsync(string lobbyCode)
    {
        _logger.LogInformation("Getting phase lobby by code {LobbyCode}", lobbyCode);

        // This would need proper implementation for knockout phases
        throw new NotImplementedException("Phase lobby retrieval by code is not yet implemented");
    }

    public async Task<CompetitionPhaseLobbyDto> GetPhaseLobbyByIdAsync(Guid lobbyId)
    {
        _logger.LogInformation("Getting phase lobby by ID {LobbyId}", lobbyId);

        // This would need proper implementation for knockout phases
        throw new NotImplementedException("Phase lobby retrieval by ID is not yet implemented");
    }

    public async Task DeletePhaseLobbyAsync(Guid lobbyId)
    {
        _logger.LogInformation("Deleting phase lobby {LobbyId}", lobbyId);

        try
        {
            // Implementation would delete the lobby and associated teams
            // For now, we'll throw not implemented as this requires careful handling
            throw new NotImplementedException("Delete phase lobby requires careful implementation to handle associated data");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting phase lobby {LobbyId}", lobbyId);
            throw;
        }
    }

    public async Task SetLobbyWinnerAsync(Guid lobbyId, Guid winnerTeamId)
    {
        _logger.LogInformation("Setting lobby winner for lobby {LobbyId}, winner team {WinnerTeamId}",
            lobbyId, winnerTeamId);

        try
        {
            // This would typically be handled automatically by ProcessGameResult
            // Manual override might be needed for exceptional cases
            throw new NotImplementedException("Manual winner setting requires additional validation logic");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting lobby winner for lobby {LobbyId}", lobbyId);
            throw;
        }
    }

    // Match Management Implementation
    public async Task ScheduleMatchAsync(Guid lobbyId, DateTime scheduledAt, string? customMessage = null)
    {
        _logger.LogInformation("Scheduling match for lobby {LobbyId} at {ScheduledAt}", lobbyId, scheduledAt);

        try
        {
            // Update lobby with scheduled time
            var parameters = new
            {
                LobbyId = lobbyId,
                MatchScheduledAt = scheduledAt,
                MatchStatus = "Scheduled"
            };

            await _competitionRepository.ExecuteStoredProcedureAsync(
                "UPDATE CompetitionPhaseLobbies SET MatchScheduledAt = @MatchScheduledAt, MatchStatus = @MatchStatus WHERE Id = @LobbyId",
                parameters);

            // Send notification if requested
            await SendMatchNotificationAsync(lobbyId, "MatchScheduled");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error scheduling match for lobby {LobbyId}", lobbyId);
            throw;
        }
    }

    public async Task SendMatchNotificationAsync(Guid lobbyId, string notificationType = "MatchScheduled")
    {
        _logger.LogInformation("Sending match notification for lobby {LobbyId}, type {NotificationType}",
            lobbyId, notificationType);

        try
        {
            // Get lobby details for notification
            var lobby = await GetPhaseLobbyByIdAsync(lobbyId);

            // Create notification record
            var parameters = new
            {
                Id = Guid.NewGuid(),
                LobbyId = lobbyId,
                NotificationType = notificationType,
                Status = "Sent",
                EmailContent = $"Match notification for {lobby.Phase} phase"
            };

            await _competitionRepository.ExecuteStoredProcedureAsync(
                "INSERT INTO MatchNotifications (Id, LobbyId, NotificationType, Status, EmailContent, SentAt) VALUES (@Id, @LobbyId, @NotificationType, @Status, @EmailContent, GETUTCDATE())",
                parameters);

            // Update lobby notification timestamp
            await _competitionRepository.ExecuteStoredProcedureAsync(
                "UPDATE CompetitionPhaseLobbies SET NotificationSentAt = GETUTCDATE() WHERE Id = @LobbyId",
                new { LobbyId = lobbyId });

            _logger.LogInformation("Match notification sent successfully for lobby {LobbyId}", lobbyId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending match notification for lobby {LobbyId}", lobbyId);
            throw;
        }
    }

    public async Task<MatchResultDto> ProcessGameResultAsync(Guid gameId, Guid phaseLobbyId)
    {
        _logger.LogInformation("Processing game result for game {GameId} in lobby {PhaseLobbyId}",
            gameId, phaseLobbyId);

        try
        {
            var parameters = new
            {
                GameId = gameId,
                PhaseLobbyId = phaseLobbyId
            };

            // This would need proper implementation for knockout phases
            throw new NotImplementedException("Game result processing is not yet implemented");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing game result for game {GameId}", gameId);
            throw;
        }
    }

    public async Task<MatchResultDto> EvaluateMatchResultAsync(Guid lobbyId)
    {
        _logger.LogInformation("Evaluating match result for lobby {LobbyId}", lobbyId);

        try
        {
            var lobby = await GetPhaseLobbyByIdAsync(lobbyId);
            var team1Wins = lobby.Games.Count(g => g.WinnerTeam == 1);
            var team2Wins = lobby.Games.Count(g => g.WinnerTeam == 2);

            var isComplete = team1Wins >= lobby.RequiredWins || team2Wins >= lobby.RequiredWins;
            var winnerTeamNumber = team1Wins >= lobby.RequiredWins ? 1 :
                                 team2Wins >= lobby.RequiredWins ? 2 : (int?)null;

            return new MatchResultDto
            {
                LobbyId = lobbyId,
                IsMatchComplete = isComplete,
                Team1Wins = team1Wins,
                Team2Wins = team2Wins,
                RequiredWins = lobby.RequiredWins,
                WinnerTeamNumber = winnerTeamNumber,
                TotalGames = lobby.Games.Count
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error evaluating match result for lobby {LobbyId}", lobbyId);
            throw;
        }
    }

    public async Task<bool> IsMatchCompleteAsync(Guid lobbyId)
    {
        _logger.LogInformation("Checking if match is complete for lobby {LobbyId}", lobbyId);

        try
        {
            var result = await EvaluateMatchResultAsync(lobbyId);
            return result.IsMatchComplete;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking match completion for lobby {LobbyId}", lobbyId);
            throw;
        }
    }

    public async Task<BracketDto> GenerateBracketAsync(Guid competitionId, string phase)
    {
        throw new NotImplementedException("GenerateBracketAsync requires full implementation");
    }

    public async Task<List<BracketDto>> GetCompetitionBracketsAsync(Guid competitionId)
    {
        throw new NotImplementedException("GetCompetitionBracketsAsync requires full implementation");
    }

    public async Task<CompetitionTeamPhaseStatsDto> CreatePhaseStatsAsync(Guid competitionTeamId, string phase)
    {
        throw new NotImplementedException("CreatePhaseStatsAsync requires full implementation");
    }

    public async Task<CompetitionTeamPhaseStatsDto> UpdatePhaseStatsAsync(Guid competitionTeamId, string phase, int points, int bonusPoints, int ballsWon)
    {
        throw new NotImplementedException("UpdatePhaseStatsAsync requires full implementation");
    }

    public async Task<List<CompetitionTeamPhaseStatsDto>> GetTeamPhaseStatsAsync(Guid competitionTeamId)
    {
        throw new NotImplementedException("GetTeamPhaseStatsAsync requires full implementation");
    }

    public async Task ProcessPhaseEndAsync(Guid competitionId)
    {
        throw new NotImplementedException("ProcessPhaseEndAsync requires full implementation");
    }

    public async Task<bool> CanAdvanceToNextPhaseAsync(Guid competitionId)
    {
        throw new NotImplementedException("CanAdvanceToNextPhaseAsync requires full implementation");
    }

    public async Task<List<CompetitionTeamDto>> CalculatePhaseRankingsAsync(Guid competitionId, string phase)
    {
        throw new NotImplementedException("CalculatePhaseRankingsAsync requires full implementation");
    }

    private CompetitionDto MapToCompetitionDto(Competition competition)
    {
        return new CompetitionDto
        {
            Id = competition.Id,
            Name = competition.Name,
            Description = competition.Description,
            StartDate = competition.StartDate,
            EndDate = competition.EndDate,
            Status = competition.Status,
            MaxTeams = competition.MaxTeams,
            EntryFee = competition.EntryFee,
            Phase = competition.Phase,
            PhaseEndDate = competition.PhaseEndDate,
            CreatedAt = competition.CreatedAt
        };
    }

    private async Task<CompetitionTeamDto> MapToCompetitionTeamDtoAsync(CompetitionTeam team)
    {
        var player1 = await _userRepository.GetByIdAsync(team.Player1Id);
        var player2 = team.Player2Id.HasValue ? await _userRepository.GetByIdAsync(team.Player2Id.Value) : null;

        return new CompetitionTeamDto
        {
            Id = team.Id,
            TeamName = team.TeamName,
            Player1 = player1 != null ? new PlayerDto { Id = player1.Id, Username = player1.Username } : new PlayerDto(),
            Player2 = player2 != null ? new PlayerDto { Id = player2.Id, Username = player2.Username } : null,
            InviteCode = team.InviteCode,
            GamesPlayed = team.GamesPlayed,
            Points = team.Points,
            BonusPoints = team.BonusPoints,
            MaxGames = team.MaxGames,
            IsComplete = team.IsComplete,
            RegisteredAt = team.RegisteredAt,
            CompletedAt = team.CompletedAt,
            Phase = team.Phase,
            IsEliminated = team.IsEliminated,
            AdvancedToNextPhase = team.AdvancedToNextPhase,
            PhaseEliminatedAt = team.PhaseEliminatedAt
        };
    }

    private CompetitionTeamDto MapToCompetitionTeamDto(CompetitionTeam team)
    {
        return new CompetitionTeamDto
        {
            Id = team.Id,
            TeamName = team.TeamName,
            InviteCode = team.InviteCode,
            GamesPlayed = team.GamesPlayed,
            Points = team.Points,
            BonusPoints = team.BonusPoints,
            MaxGames = team.MaxGames,
            IsComplete = team.IsComplete,
            RegisteredAt = team.RegisteredAt,
            CompletedAt = team.CompletedAt,
            Phase = team.Phase,
            IsEliminated = team.IsEliminated,
            AdvancedToNextPhase = team.AdvancedToNextPhase,
            PhaseEliminatedAt = team.PhaseEliminatedAt
        };
    }

    private int GetAdvancingCountForPhase(string currentPhase)
    {
        return currentPhase switch
        {
            "Leaderboard" => 32,  // Top 32 teams advance to Top32
            "Top32" => 16,        // Top 16 teams advance to Top16
            "Top16" => 8,         // Top 8 teams advance to Top8
            "Top8" => 4,          // Top 4 teams advance to Top4
            "Top4" => 2,          // Top 2 teams advance to Final
            "Final" => 1,         // Winner
            _ => 0
        };
    }

    // Admin Management Implementation
    public async Task<List<AdminLobbyViewDto>> GetAdminLobbyViewAsync(Guid competitionId, string? phase = null)
    {
        _logger.LogInformation("Getting admin lobby view for competition {CompetitionId} phase {Phase}",
            competitionId, phase);

        try
        {
            // This would need proper implementation for knockout phases
            return new List<AdminLobbyViewDto>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting admin lobby view for competition {CompetitionId}", competitionId);
            throw;
        }
    }

    public async Task<List<CompetitionTeamDto>> GetPhaseWinnersAsync(Guid competitionId, string phase)
    {
        _logger.LogInformation("Getting phase winners for competition {CompetitionId} phase {Phase}",
            competitionId, phase);

        try
        {
            // This would need proper implementation for knockout phases
            return new List<CompetitionTeamDto>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting phase winners for competition {CompetitionId}", competitionId);
            throw;
        }
    }

    public async Task AdvancePhaseAsync(Guid competitionId, string currentPhase, string nextPhase)
    {
        _logger.LogInformation("Advancing phase for competition {CompetitionId} from {CurrentPhase} to {NextPhase}",
            competitionId, currentPhase, nextPhase);

        try
        {
            // This method is for knockout phases only
            // For Leaderboard phase, use AdvanceCompetitionPhaseAsync instead
            throw new NotImplementedException($"Knockout phase advancement from {currentPhase} to {nextPhase} is not yet implemented");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error advancing phase for competition {CompetitionId}", competitionId);
            throw;
        }
    }

    public async Task<bool> CanAdvancePhaseAsync(Guid competitionId, string phase)
    {
        _logger.LogInformation("Checking if phase can be advanced for competition {CompetitionId} phase {Phase}",
            competitionId, phase);

        try
        {
            // For Leaderboard phase, we can always advance (no matches to complete)
            if (phase == "Leaderboard")
            {
                _logger.LogInformation("Leaderboard phase can always be advanced for competition {CompetitionId}", competitionId);
                return true;
            }

            // For knockout phases, check if all matches in the phase are completed
            var lobbies = await GetPhaseLobbiesAsync(competitionId, phase);
            var incompleteMatches = lobbies.Where(l => l.MatchStatus != "Completed").ToList();

            var canAdvance = !incompleteMatches.Any();

            _logger.LogInformation("Phase advancement check for competition {CompetitionId} phase {Phase}: {CanAdvance} ({IncompleteCount} incomplete matches)",
                competitionId, phase, canAdvance, incompleteMatches.Count);

            return canAdvance;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking phase advancement for competition {CompetitionId}", competitionId);
            throw;
        }
    }
}
