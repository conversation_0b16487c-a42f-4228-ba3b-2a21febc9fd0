-- Fixed stored procedure for creating all phase matches
USE [GoldRushThunee]
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[SP_CreateAllPhaseMatches]') AND type in (N'P', N'PC'))
DROP PROCEDURE [dbo].[SP_CreateAllPhaseMatches]
GO

CREATE PROCEDURE [dbo].[SP_CreateAllPhaseMatches]
    @CompetitionId UNIQUEIDENTIFIER,
    @Phase NVARCHAR(40),
    @StartTime DATETIME2,
    @MatchIntervalMinutes INT = 60,
    @BestOfGames INT = 3
AS
BEGIN
    SET NOCOUNT ON;
    
    -- Skip admin validation for now - allow any GUID
    
    DECLARE @RequiredWins INT = CEILING(@BestOfGames / 2.0);
    DECLARE @MatchesCreated INT = 0;
    DECLARE @TotalTeams INT = 0;
    DECLARE @LobbyCode NVARCHAR(24);
    DECLARE @CurrentTime DATETIME2 = @StartTime;
    
    -- Get all teams that advanced to this phase and are not eliminated
    SELECT @TotalTeams = COUNT(*)
    FROM CompetitionTeams 
    WHERE CompetitionId = @CompetitionId 
      AND AdvancedToNextPhase = 1 
      AND IsEliminated = 0;
    
    IF @TotalTeams = 0
    BEGIN
        RAISERROR('No teams found for this competition phase', 16, 1);
        RETURN;
    END
    
    -- Create temporary table to hold team pairings
    CREATE TABLE #TeamPairings (
        RowNum INT,
        TeamId UNIQUEIDENTIFIER,
        TeamName NVARCHAR(100),
        Player1Id UNIQUEIDENTIFIER,
        Player1Name NVARCHAR(100),
        Player2Id UNIQUEIDENTIFIER,
        Player2Name NVARCHAR(100)
    );
    
    -- Insert teams ordered by points (highest first)
    INSERT INTO #TeamPairings (RowNum, TeamId, TeamName, Player1Id, Player1Name, Player2Id, Player2Name)
    SELECT 
        ROW_NUMBER() OVER (ORDER BY (ct.Points + ct.BonusPoints) DESC, ct.RegisteredAt ASC),
        ct.Id,
        ct.TeamName,
        ct.Player1Id,
        u1.Username,
        ct.Player2Id,
        u2.Username
    FROM CompetitionTeams ct
    INNER JOIN Users u1 ON ct.Player1Id = u1.Id
    LEFT JOIN Users u2 ON ct.Player2Id = u2.Id
    WHERE ct.CompetitionId = @CompetitionId 
      AND ct.AdvancedToNextPhase = 1 
      AND ct.IsEliminated = 0;
    
    -- Create matches by pairing teams (1st vs last, 2nd vs 2nd-last, etc.)
    DECLARE @Team1Id UNIQUEIDENTIFIER, @Team2Id UNIQUEIDENTIFIER;
    DECLARE @Team1Name NVARCHAR(100), @Team2Name NVARCHAR(100);
    DECLARE @Player1Id UNIQUEIDENTIFIER, @Player2Id UNIQUEIDENTIFIER;
    DECLARE @Player3Id UNIQUEIDENTIFIER, @Player4Id UNIQUEIDENTIFIER;
    DECLARE @Player1Name NVARCHAR(100), @Player2Name NVARCHAR(100);
    DECLARE @Player3Name NVARCHAR(100), @Player4Name NVARCHAR(100);
    DECLARE @i INT = 1;
    DECLARE @MaxPairs INT = @TotalTeams / 2;
    
    WHILE @i <= @MaxPairs
    BEGIN
        -- Get team 1 (from top)
        SELECT 
            @Team1Id = TeamId, @Team1Name = TeamName,
            @Player1Id = Player1Id, @Player1Name = Player1Name,
            @Player2Id = Player2Id, @Player2Name = Player2Name
        FROM #TeamPairings 
        WHERE RowNum = @i;
        
        -- Get team 2 (from bottom)
        SELECT 
            @Team2Id = TeamId, @Team2Name = TeamName,
            @Player3Id = Player1Id, @Player3Name = Player1Name,
            @Player4Id = Player2Id, @Player4Name = Player2Name
        FROM #TeamPairings 
        WHERE RowNum = (@TotalTeams - @i + 1);
        
        -- Create multiple matches for Best-of format
        DECLARE @GameNumber INT = 1;
        DECLARE @GameTime DATETIME2 = @CurrentTime;
        
        WHILE @GameNumber <= @BestOfGames
        BEGIN
            -- Generate unique lobby code for each game
            SET @LobbyCode = 'KO' + RIGHT('000000' + CAST(ABS(CHECKSUM(NEWID())) % 1000000 AS VARCHAR(6)), 6);
            
            -- Ensure lobby code is unique
            WHILE EXISTS (SELECT 1 FROM CompetitionPhaseLobbies WHERE LobbyCode = @LobbyCode)
            BEGIN
                SET @LobbyCode = 'KO' + RIGHT('000000' + CAST(ABS(CHECKSUM(NEWID())) % 1000000 AS VARCHAR(6)), 6);
            END
            
            -- Insert individual match lobby and get the ID
            DECLARE @NewLobbyId UNIQUEIDENTIFIER = NEWID();
            
            INSERT INTO CompetitionPhaseLobbies (
                Id, CompetitionId, Phase, LobbyCode,
                MatchScheduledAt, BestOfGames, RequiredWins, MatchStatus
            )
            VALUES (
                @NewLobbyId,
                @CompetitionId,
                @Phase + '_Game' + CAST(@GameNumber AS NVARCHAR(2)), -- e.g., "Top32_Game1", "Top32_Game2"
                @LobbyCode,
                @GameTime,
                1, -- Each individual game is single game
                1, -- First to 1 win (since it's individual game)
                'Scheduled'
            );

            -- Insert team participants for this game
            INSERT INTO CompetitionPhaseLobbyTeams (LobbyId, CompetitionTeamId, IsWinner)
            VALUES
                (@NewLobbyId, @Team1Id, 0),
                (@NewLobbyId, @Team2Id, 0);
            
            SET @MatchesCreated = @MatchesCreated + 1;
            SET @GameNumber = @GameNumber + 1;
            SET @GameTime = DATEADD(MINUTE, @MatchIntervalMinutes, @GameTime);
        END
        
        SET @i = @i + 1;
        -- Move to next team pairing with additional gap
        SET @CurrentTime = DATEADD(MINUTE, @MatchIntervalMinutes * (@BestOfGames + 1), @CurrentTime);
    END
    
    DROP TABLE #TeamPairings;
    
    -- Return results
    SELECT 
        @MatchesCreated as MatchesCreated,
        @TotalTeams as TotalTeams,
        @LobbyCode as LastLobbyCode
END
GO

PRINT 'SP_CreateAllPhaseMatches fixed stored procedure created successfully!'
