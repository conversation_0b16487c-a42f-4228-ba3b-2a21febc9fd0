-- Test script to advance competition phase from Leaderboard to Top32
-- Competition ID: 4860C19D-E3F3-4E2D-B359-275527461BD6

USE [GoldRushThunee]
GO

-- First, let's see the current state
SELECT 'BEFORE ADVANCEMENT' AS Status;
SELECT Phase, COUNT(*) AS TeamCount 
FROM CompetitionTeams 
WHERE CompetitionId = '4860C19D-E3F3-4E2D-B359-275527461BD6'
GROUP BY Phase;

-- Get top 32 teams by total points (Points + BonusPoints)
SELECT TOP 32 
    Id,
    TeamName,
    Points,
    BonusPoints,
    (Points + BonusPoints) AS TotalPoints,
    GamesPlayed,
    RegisteredAt
FROM CompetitionTeams 
WHERE CompetitionId = '4860C19D-E3F3-4E2D-B359-275527461BD6'
  AND Phase = 'Leaderboard'
  AND IsEliminated = 0
ORDER BY (Points + BonusPoints) DESC, Points DESC, GamesPlayed ASC, RegisteredAt ASC;

-- Get the team IDs as comma-separated string for the top 32 teams
DECLARE @EligibleTeamIds NVARCHAR(MAX);
SELECT @EligibleTeamIds = STRING_AGG(CAST(Id AS NVARCHAR(36)), ',')
FROM (
    SELECT TOP 32 Id
    FROM CompetitionTeams 
    WHERE CompetitionId = '4860C19D-E3F3-4E2D-B359-275527461BD6'
      AND Phase = 'Leaderboard'
      AND IsEliminated = 0
    ORDER BY (Points + BonusPoints) DESC, Points DESC, GamesPlayed ASC, RegisteredAt ASC
) AS TopTeams;

PRINT 'Eligible Team IDs: ' + @EligibleTeamIds;

-- Execute the stored procedure
EXEC SP_AdvanceCompetitionPhase 
    @CompetitionId = '4860C19D-E3F3-4E2D-B359-275527461BD6',
    @NewPhase = 'Top32',
    @EligibleTeamIds = @EligibleTeamIds,
    @PhaseEndDate = '2025-08-10 23:59:59',
    @MaxGamesPerPhase = 5;

-- Check the results
SELECT 'AFTER ADVANCEMENT' AS Status;
SELECT Phase, COUNT(*) AS TeamCount 
FROM CompetitionTeams 
WHERE CompetitionId = '4860C19D-E3F3-4E2D-B359-275527461BD6'
GROUP BY Phase;

-- Show advanced teams
SELECT 'ADVANCED TEAMS' AS Status;
SELECT TeamName, Points, BonusPoints, (Points + BonusPoints) AS TotalPoints, Phase, AdvancedToNextPhase
FROM CompetitionTeams 
WHERE CompetitionId = '4860C19D-E3F3-4E2D-B359-275527461BD6'
  AND Phase = 'Top32'
ORDER BY (Points + BonusPoints) DESC;

-- Show eliminated teams
SELECT 'ELIMINATED TEAMS' AS Status;
SELECT TeamName, Points, BonusPoints, (Points + BonusPoints) AS TotalPoints, Phase, IsEliminated, PhaseEliminatedAt
FROM CompetitionTeams 
WHERE CompetitionId = '4860C19D-E3F3-4E2D-B359-275527461BD6'
  AND IsEliminated = 1
ORDER BY (Points + BonusPoints) DESC;

-- Check competition phase
SELECT Phase, PhaseEndDate, MaxGamesPerPhase, UpdatedAt
FROM Competitions 
WHERE Id = '4860C19D-E3F3-4E2D-B359-275527461BD6';
