-- Create stored procedure for creating all phase matches
USE [GoldRushThunee]
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[SP_CreateAllPhaseMatches]') AND type in (N'P', N'PC'))
DROP PROCEDURE [dbo].[SP_CreateAllPhaseMatches]
GO

CREATE PROCEDURE [dbo].[SP_CreateAllPhaseMatches]
    @CompetitionId UNIQUEIDENTIFIER,
    @Phase NVARCHAR(40),
    @AdminId UNIQUEIDENTIFIER,
    @StartTime DATETIME2,
    @MatchIntervalMinutes INT = 60,
    @BestOfGames INT = 3
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @RequiredWins INT = CEILING(@BestOfGames / 2.0);
    DECLARE @MatchesCreated INT = 0;
    DECLARE @TotalTeams INT = 0;
    DECLARE @LobbyCode NVARCHAR(24);
    DECLARE @CurrentTime DATETIME2 = @StartTime;
    
    -- Get all teams that advanced to this phase and are not eliminated
    SELECT @TotalTeams = COUNT(*)
    FROM CompetitionTeams 
    WHERE CompetitionId = @CompetitionId 
      AND AdvancedToNextPhase = 1 
      AND IsEliminated = 0;
    
    -- Create temporary table to hold team pairings
    CREATE TABLE #TeamPairings (
        RowNum INT,
        TeamId UNIQUEIDENTIFIER,
        TeamName NVARCHAR(100),
        Player1Id UNIQUEIDENTIFIER,
        Player1Name NVARCHAR(100),
        Player2Id UNIQUEIDENTIFIER,
        Player2Name NVARCHAR(100)
    );
    
    -- Insert teams ordered by points (highest first)
    INSERT INTO #TeamPairings (RowNum, TeamId, TeamName, Player1Id, Player1Name, Player2Id, Player2Name)
    SELECT 
        ROW_NUMBER() OVER (ORDER BY ct.TotalPoints DESC, ct.CreatedAt ASC),
        ct.Id,
        ct.TeamName,
        ct.Player1Id,
        u1.Username,
        ct.Player2Id,
        u2.Username
    FROM CompetitionTeams ct
    INNER JOIN Users u1 ON ct.Player1Id = u1.Id
    LEFT JOIN Users u2 ON ct.Player2Id = u2.Id
    WHERE ct.CompetitionId = @CompetitionId 
      AND ct.AdvancedToNextPhase = 1 
      AND ct.IsEliminated = 0;
    
    -- Create matches by pairing teams (1st vs last, 2nd vs 2nd-last, etc.)
    DECLARE @Team1Id UNIQUEIDENTIFIER, @Team2Id UNIQUEIDENTIFIER;
    DECLARE @Team1Name NVARCHAR(100), @Team2Name NVARCHAR(100);
    DECLARE @Player1Id UNIQUEIDENTIFIER, @Player2Id UNIQUEIDENTIFIER;
    DECLARE @Player3Id UNIQUEIDENTIFIER, @Player4Id UNIQUEIDENTIFIER;
    DECLARE @Player1Name NVARCHAR(100), @Player2Name NVARCHAR(100);
    DECLARE @Player3Name NVARCHAR(100), @Player4Name NVARCHAR(100);
    DECLARE @i INT = 1;
    DECLARE @MaxPairs INT = @TotalTeams / 2;
    
    WHILE @i <= @MaxPairs
    BEGIN
        -- Get team 1 (from top)
        SELECT 
            @Team1Id = TeamId, @Team1Name = TeamName,
            @Player1Id = Player1Id, @Player1Name = Player1Name,
            @Player2Id = Player2Id, @Player2Name = Player2Name
        FROM #TeamPairings 
        WHERE RowNum = @i;
        
        -- Get team 2 (from bottom)
        SELECT 
            @Team2Id = TeamId, @Team2Name = TeamName,
            @Player3Id = Player1Id, @Player3Name = Player1Name,
            @Player4Id = Player2Id, @Player4Name = Player2Name
        FROM #TeamPairings 
        WHERE RowNum = (@TotalTeams - @i + 1);
        
        -- Generate unique lobby code
        SET @LobbyCode = 'KO' + RIGHT('000000' + CAST(ABS(CHECKSUM(NEWID())) % 1000000 AS VARCHAR(6)), 6);
        
        -- Ensure lobby code is unique
        WHILE EXISTS (SELECT 1 FROM CompetitionPhaseLobbies WHERE LobbyCode = @LobbyCode)
        BEGIN
            SET @LobbyCode = 'KO' + RIGHT('000000' + CAST(ABS(CHECKSUM(NEWID())) % 1000000 AS VARCHAR(6)), 6);
        END
        
        -- Insert the match lobby
        INSERT INTO CompetitionPhaseLobbies (
            CompetitionId, Phase, LobbyCode, CreatedByAdminId, 
            MatchScheduledAt, BestOfGames, RequiredWins, MatchStatus
        )
        VALUES (
            @CompetitionId, @Phase, @LobbyCode, @AdminId,
            @CurrentTime, @BestOfGames, @RequiredWins, 'Scheduled'
        );
        
        DECLARE @LobbyId UNIQUEIDENTIFIER = SCOPE_IDENTITY();
        
        -- Insert team participants
        INSERT INTO CompetitionPhaseTeams (LobbyId, TeamId, TeamNumber)
        VALUES 
            (@LobbyId, @Team1Id, 1),
            (@LobbyId, @Team2Id, 2);
        
        SET @MatchesCreated = @MatchesCreated + 1;
        SET @CurrentTime = DATEADD(MINUTE, @MatchIntervalMinutes, @CurrentTime);
        SET @i = @i + 1;
    END
    
    DROP TABLE #TeamPairings;
    
    -- Return results
    SELECT 
        @MatchesCreated as MatchesCreated,
        @TotalTeams as TotalTeams,
        @LobbyCode as LastLobbyCode
END
GO

PRINT 'SP_CreateAllPhaseMatches stored procedure created successfully!'
