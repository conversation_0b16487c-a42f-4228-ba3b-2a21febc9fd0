-- Comprehensive stored procedure to advance competition from Leaderboard to Top32
-- This handles everything: team selection, phase updates, eliminations, and competition updates

USE [GoldRushThunee]
GO

-- Drop the procedure if it exists
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'SP_AdvanceCompetitionToTop32')
    DROP PROCEDURE SP_AdvanceCompetitionToTop32
GO

CREATE PROCEDURE SP_AdvanceCompetitionToTop32
    @CompetitionId UNIQUEIDENTIFIER,
    @NewPhase NVARCHAR(50) = 'Top32',
    @PhaseEndDate DATETIME2 = NULL,
    @MaxGamesPerPhase BIGINT = 10
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @TeamsAdvanced INT = 0;
    DECLARE @TeamsEliminated INT = 0;
    DECLARE @ErrorMessage NVARCHAR(500);
    
    BEGIN TRY
        BEGIN TRANSACTION;
        
        -- Validate competition exists and is in Leaderboard phase
        IF NOT EXISTS (SELECT 1 FROM Competitions WHERE Id = @CompetitionId AND Phase = 'Leaderboard')
        BEGIN
            RAISERROR('Competition not found or not in Leaderboard phase', 16, 1);
            RETURN;
        END
        
        -- Set default phase end date if not provided
        IF @PhaseEndDate IS NULL
            SET @PhaseEndDate = DATEADD(DAY, 7, GETUTCDATE());
        
        -- Create a temp table to hold the top 32 teams
        CREATE TABLE #Top32Teams (
            Id UNIQUEIDENTIFIER,
            TeamName NVARCHAR(100),
            TotalPoints INT,
            Points INT,
            BonusPoints INT,
            GamesPlayed INT,
            RegisteredAt DATETIME2,
            RowNum INT
        );
        
        -- Get top 32 teams by total points (same logic as working SQL script)
        INSERT INTO #Top32Teams (Id, TeamName, TotalPoints, Points, BonusPoints, GamesPlayed, RegisteredAt, RowNum)
        SELECT 
            Id,
            TeamName,
            (Points + BonusPoints) AS TotalPoints,
            Points,
            BonusPoints,
            GamesPlayed,
            RegisteredAt,
            ROW_NUMBER() OVER (
                ORDER BY (Points + BonusPoints) DESC, 
                         Points DESC, 
                         GamesPlayed ASC, 
                         RegisteredAt ASC
            ) AS RowNum
        FROM CompetitionTeams 
        WHERE CompetitionId = @CompetitionId
          AND Phase = 'Leaderboard'
          AND IsEliminated = 0;
        
        -- Log the teams that will advance
        PRINT 'TOP 32 TEAMS TO ADVANCE:';
        SELECT 
            RowNum,
            TeamName,
            TotalPoints,
            Points,
            BonusPoints,
            GamesPlayed
        FROM #Top32Teams 
        WHERE RowNum <= 32
        ORDER BY RowNum;
        
        -- Advance top 32 teams to the new phase
        UPDATE CompetitionTeams 
        SET Phase = @NewPhase,
            AdvancedToNextPhase = 1,
            MaxGames = @MaxGamesPerPhase
        WHERE Id IN (SELECT Id FROM #Top32Teams WHERE RowNum <= 32);
        
        SET @TeamsAdvanced = @@ROWCOUNT;
        
        -- Eliminate remaining teams
        UPDATE CompetitionTeams 
        SET IsEliminated = 1,
            PhaseEliminatedAt = GETUTCDATE()
        WHERE CompetitionId = @CompetitionId
          AND Phase = 'Leaderboard'
          AND IsEliminated = 0
          AND Id NOT IN (SELECT Id FROM #Top32Teams WHERE RowNum <= 32);
        
        SET @TeamsEliminated = @@ROWCOUNT;
        
        -- Update the competition phase
        UPDATE Competitions 
        SET Phase = @NewPhase,
            PhaseEndDate = @PhaseEndDate,
            MaxGamesPerPhase = @MaxGamesPerPhase,
            UpdatedAt = GETUTCDATE()
        WHERE Id = @CompetitionId;
        
        -- Clean up temp table
        DROP TABLE #Top32Teams;
        
        COMMIT TRANSACTION;
        
        -- Return success result
        SELECT 
            'Success' AS Result,
            @TeamsAdvanced AS TeamsAdvanced,
            @TeamsEliminated AS TeamsEliminated,
            @NewPhase AS NewPhase,
            @PhaseEndDate AS PhaseEndDate,
            @MaxGamesPerPhase AS MaxGamesPerPhase;
        
        PRINT 'SUCCESS: Advanced ' + CAST(@TeamsAdvanced AS NVARCHAR(10)) + ' teams to ' + @NewPhase + 
              ', eliminated ' + CAST(@TeamsEliminated AS NVARCHAR(10)) + ' teams';
        
    END TRY
    BEGIN CATCH
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
        
        -- Clean up temp table if it exists
        IF OBJECT_ID('tempdb..#Top32Teams') IS NOT NULL
            DROP TABLE #Top32Teams;
        
        SET @ErrorMessage = ERROR_MESSAGE();
        
        -- Return error result
        SELECT 
            'Error' AS Result,
            0 AS TeamsAdvanced,
            0 AS TeamsEliminated,
            @ErrorMessage AS ErrorMessage;
        
        PRINT 'ERROR: ' + @ErrorMessage;
        
        RAISERROR(@ErrorMessage, 16, 1);
    END CATCH
END
GO

-- Test the stored procedure
PRINT 'Testing SP_AdvanceCompetitionToTop32...';

-- First, let's see the current state
SELECT 'BEFORE EXECUTION' AS Status;
SELECT Phase, COUNT(*) AS TeamCount 
FROM CompetitionTeams 
WHERE CompetitionId = '4860C19D-E3F3-4E2D-B359-275527461BD6'
GROUP BY Phase;

-- Execute the stored procedure
EXEC SP_AdvanceCompetitionToTop32 
    @CompetitionId = '4860C19D-E3F3-4E2D-B359-275527461BD6',
    @NewPhase = 'Top32',
    @PhaseEndDate = '2025-08-10 23:59:59',
    @MaxGamesPerPhase = 10;

-- Check the results
SELECT 'AFTER EXECUTION' AS Status;
SELECT Phase, COUNT(*) AS TeamCount 
FROM CompetitionTeams 
WHERE CompetitionId = '4860C19D-E3F3-4E2D-B359-275527461BD6'
GROUP BY Phase;

-- Show advanced teams
SELECT 'ADVANCED TEAMS' AS Status;
SELECT TeamName, Points, BonusPoints, (Points + BonusPoints) AS TotalPoints, Phase, AdvancedToNextPhase
FROM CompetitionTeams 
WHERE CompetitionId = '4860C19D-E3F3-4E2D-B359-275527461BD6'
  AND Phase = 'Top32'
ORDER BY (Points + BonusPoints) DESC;

-- Show eliminated teams
SELECT 'ELIMINATED TEAMS' AS Status;
SELECT TeamName, Points, BonusPoints, (Points + BonusPoints) AS TotalPoints, IsEliminated, PhaseEliminatedAt
FROM CompetitionTeams 
WHERE CompetitionId = '4860C19D-E3F3-4E2D-B359-275527461BD6'
  AND IsEliminated = 1
ORDER BY (Points + BonusPoints) DESC;

-- Check competition status
SELECT 'COMPETITION STATUS' AS Status;
SELECT Phase, PhaseEndDate, MaxGamesPerPhase, UpdatedAt
FROM Competitions 
WHERE Id = '4860C19D-E3F3-4E2D-B359-275527461BD6';

PRINT 'Stored procedure test completed!';
