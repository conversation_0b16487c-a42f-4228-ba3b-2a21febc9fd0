-- Create the SP_AdvanceCompetitionToTop32 stored procedure
USE [GoldRushThunee]
GO

-- Drop the procedure if it exists
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'SP_AdvanceCompetitionToTop32')
    DROP PROCEDURE SP_AdvanceCompetitionToTop32
GO

CREATE PROCEDURE SP_AdvanceCompetitionToTop32
    @CompetitionId UNIQUEIDENTIFIER,
    @NewPhase NVARCHAR(50) = 'Top32',
    @PhaseEndDate DATETIME2 = NULL,
    @MaxGamesPerPhase BIGINT = 10
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @TeamsAdvanced INT = 0;
    DECLARE @TeamsEliminated INT = 0;
    DECLARE @ErrorMessage NVARCHAR(500);
    
    BEGIN TRY
        BEGIN TRANSACTION;
        
        -- Validate competition exists and is in Leaderboard phase
        IF NOT EXISTS (SELECT 1 FROM Competitions WHERE Id = @CompetitionId AND Phase = 'Leaderboard')
        BEGIN
            RAISERROR('Competition not found or not in Leaderboard phase', 16, 1);
            RETURN;
        END
        
        -- Set default phase end date if not provided
        IF @PhaseEndDate IS NULL
            SET @PhaseEndDate = DATEADD(DAY, 7, GETUTCDATE());
        
        -- Create a temp table to hold the top 32 teams
        CREATE TABLE #Top32Teams (
            Id UNIQUEIDENTIFIER,
            TeamName NVARCHAR(100),
            TotalPoints INT,
            Points INT,
            BonusPoints INT,
            GamesPlayed INT,
            RegisteredAt DATETIME2,
            RowNum INT
        );
        
        -- Get top 32 teams by total points (same logic as working SQL script)
        INSERT INTO #Top32Teams (Id, TeamName, TotalPoints, Points, BonusPoints, GamesPlayed, RegisteredAt, RowNum)
        SELECT 
            Id,
            TeamName,
            (Points + BonusPoints) AS TotalPoints,
            Points,
            BonusPoints,
            GamesPlayed,
            RegisteredAt,
            ROW_NUMBER() OVER (
                ORDER BY (Points + BonusPoints) DESC, 
                         Points DESC, 
                         GamesPlayed ASC, 
                         RegisteredAt ASC
            ) AS RowNum
        FROM CompetitionTeams 
        WHERE CompetitionId = @CompetitionId
          AND Phase = 'Leaderboard'
          AND IsEliminated = 0;
        
        -- Advance top 32 teams to the new phase
        UPDATE CompetitionTeams 
        SET Phase = @NewPhase,
            AdvancedToNextPhase = 1,
            MaxGames = @MaxGamesPerPhase
        WHERE Id IN (SELECT Id FROM #Top32Teams WHERE RowNum <= 32);
        
        SET @TeamsAdvanced = @@ROWCOUNT;
        
        -- Eliminate remaining teams
        UPDATE CompetitionTeams 
        SET IsEliminated = 1,
            PhaseEliminatedAt = GETUTCDATE()
        WHERE CompetitionId = @CompetitionId
          AND Phase = 'Leaderboard'
          AND IsEliminated = 0
          AND Id NOT IN (SELECT Id FROM #Top32Teams WHERE RowNum <= 32);
        
        SET @TeamsEliminated = @@ROWCOUNT;
        
        -- Update the competition phase
        UPDATE Competitions 
        SET Phase = @NewPhase,
            PhaseEndDate = @PhaseEndDate,
            MaxGamesPerPhase = @MaxGamesPerPhase,
            UpdatedAt = GETUTCDATE()
        WHERE Id = @CompetitionId;
        
        -- Clean up temp table
        DROP TABLE #Top32Teams;
        
        COMMIT TRANSACTION;
        
        -- Return success result
        SELECT 
            'Success' AS Result,
            @TeamsAdvanced AS TeamsAdvanced,
            @TeamsEliminated AS TeamsEliminated,
            @NewPhase AS NewPhase,
            @PhaseEndDate AS PhaseEndDate,
            @MaxGamesPerPhase AS MaxGamesPerPhase;
        
        PRINT 'SUCCESS: Advanced ' + CAST(@TeamsAdvanced AS NVARCHAR(10)) + ' teams to ' + @NewPhase + 
              ', eliminated ' + CAST(@TeamsEliminated AS NVARCHAR(10)) + ' teams';
        
    END TRY
    BEGIN CATCH
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
        
        -- Clean up temp table if it exists
        IF OBJECT_ID('tempdb..#Top32Teams') IS NOT NULL
            DROP TABLE #Top32Teams;
        
        SET @ErrorMessage = ERROR_MESSAGE();
        
        -- Return error result
        SELECT 
            'Error' AS Result,
            0 AS TeamsAdvanced,
            0 AS TeamsEliminated,
            @ErrorMessage AS ErrorMessage;
        
        PRINT 'ERROR: ' + @ErrorMessage;
        
        RAISERROR(@ErrorMessage, 16, 1);
    END CATCH
END
GO

PRINT 'SP_AdvanceCompetitionToTop32 stored procedure created successfully!';
