-- Advance competition phase from Top4 to Final
-- Competition ID: 4860C19D-E3F3-4E2D-B359-275527461BD6

USE [GoldRushThunee]
GO

-- First, let's see the current state
SELECT 'BEFORE ADVANCEMENT TO FINAL' AS Status;
SELECT Phase, COUNT(*) AS TeamCount 
FROM CompetitionTeams 
WHERE CompetitionId = '4860C19D-E3F3-4E2D-B359-275527461BD6'
GROUP BY Phase;

-- Get top 2 teams from Top4 phase by total points (Points + BonusPoints)
SELECT TOP 2 
    Id,
    TeamName,
    Points,
    BonusPoints,
    (Points + BonusPoints) AS TotalPoints,
    GamesPlayed,
    RegisteredAt
FROM CompetitionTeams 
WHERE CompetitionId = '4860C19D-E3F3-4E2D-B359-275527461BD6'
  AND Phase = 'Top4'
  AND IsEliminated = 0
ORDER BY (Points + BonusPoints) DESC, Points DESC, GamesPlayed ASC, RegisteredAt ASC;

-- Get the team IDs as comma-separated string for the top 2 teams
DECLARE @EligibleTeamIds NVARCHAR(MAX);
SELECT @EligibleTeamIds = STRING_AGG(CAST(Id AS NVARCHAR(36)), ',')
FROM (
    SELECT TOP 2 Id
    FROM CompetitionTeams 
    WHERE CompetitionId = '4860C19D-E3F3-4E2D-B359-275527461BD6'
      AND Phase = 'Top4'
      AND IsEliminated = 0
    ORDER BY (Points + BonusPoints) DESC, Points DESC, GamesPlayed ASC, RegisteredAt ASC
) AS TopTeams;

PRINT 'Eligible Team IDs for Final: ' + @EligibleTeamIds;

-- Execute the stored procedure
EXEC SP_AdvanceCompetitionPhase 
    @CompetitionId = '4860C19D-E3F3-4E2D-B359-275527461BD6',
    @NewPhase = 'Final',
    @EligibleTeamIds = @EligibleTeamIds,
    @PhaseEndDate = '2025-08-30 23:59:59',
    @MaxGamesPerPhase = 5;

-- Check the results
SELECT 'AFTER ADVANCEMENT TO FINAL' AS Status;
SELECT Phase, COUNT(*) AS TeamCount 
FROM CompetitionTeams 
WHERE CompetitionId = '4860C19D-E3F3-4E2D-B359-275527461BD6'
GROUP BY Phase;

-- Show final teams
SELECT 'FINAL TEAMS' AS Status;
SELECT TeamName, Points, BonusPoints, (Points + BonusPoints) AS TotalPoints, Phase, AdvancedToNextPhase
FROM CompetitionTeams 
WHERE CompetitionId = '4860C19D-E3F3-4E2D-B359-275527461BD6'
  AND Phase = 'Final'
ORDER BY (Points + BonusPoints) DESC;

-- Check competition phase
SELECT 'COMPETITION STATUS' AS Status;
SELECT Phase, PhaseEndDate, MaxGamesPerPhase, UpdatedAt
FROM Competitions 
WHERE Id = '4860C19D-E3F3-4E2D-B359-275527461BD6';
