-- Reset competition back to Leaderboard phase
-- Competition ID: 4860C19D-E3F3-4E2D-B359-275527461BD6

USE [GoldRushThunee]
GO

PRINT 'Resetting competition to Leaderboard phase...'

-- Reset the competition phase
UPDATE Competitions 
SET Phase = 'Leaderboard',
    PhaseEndDate = '2025-08-06 07:49:33.1500000',
    MaxGamesPerPhase = 10,
    UpdatedAt = GETUTCDATE()
WHERE Id = '4860C19D-E3F3-4E2D-B359-275527461BD6';

-- Reset all teams back to Leaderboard phase
UPDATE CompetitionTeams 
SET Phase = 'Leaderboard',
    AdvancedToNextPhase = 0,
    IsEliminated = 0,
    PhaseEliminatedAt = NULL
WHERE CompetitionId = '4860C19D-E3F3-4E2D-B359-275527461BD6';

-- Verify the reset
SELECT 'AFTER RESET' AS Status;
SELECT Phase, COUNT(*) AS TeamCount 
FROM CompetitionTeams 
WHERE CompetitionId = '4860C19D-E3F3-4E2D-B359-275527461BD6'
GROUP BY Phase;

SELECT 'COMPETITION STATUS' AS Status;
SELECT Phase, PhaseEndDate, MaxGamesPerPhase, UpdatedAt
FROM Competitions 
WHERE Id = '4860C19D-E3F3-4E2D-B359-275527461BD6';

SELECT 'TEAM STATUS SUMMARY' AS Status;
SELECT 
    COUNT(*) AS TotalTeams,
    SUM(CASE WHEN AdvancedToNextPhase = 1 THEN 1 ELSE 0 END) AS AdvancedTeams,
    SUM(CASE WHEN IsEliminated = 1 THEN 1 ELSE 0 END) AS EliminatedTeams
FROM CompetitionTeams 
WHERE CompetitionId = '4860C19D-E3F3-4E2D-B359-275527461BD6';

PRINT 'Reset completed successfully!'
