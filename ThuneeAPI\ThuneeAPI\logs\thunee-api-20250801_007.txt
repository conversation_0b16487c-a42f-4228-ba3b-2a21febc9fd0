2025-08-01 14:09:07.892 +02:00 [INF] Starting Thunee API Server
2025-08-01 14:09:41.823 +02:00 [INF] Attempting to advance competition "4860c19d-e3f3-4e2d-b359-275527461bd6" to phase Top32
2025-08-01 14:09:41.844 +02:00 [INF] Advancing competition "4860c19d-e3f3-4e2d-b359-275527461bd6" to phase Top32
2025-08-01 14:09:42.588 +02:00 [INF] Executing SP_AdvanceCompetitionToTop32 for competition "4860c19d-e3f3-4e2d-b359-275527461bd6" to phase Top32
2025-08-01 14:09:42.609 +02:00 [ERR] Error advancing competition "4860c19d-e3f3-4e2d-b359-275527461bd6" to phase Top32
System.InvalidOperationException: Stored procedure 'SP_AdvanceCompetitionToTop32' not found in resources
   at ThuneeAPI.Infrastructure.Helpers.StoredProcedureHelper.GetStoredProcedure(String procedureName) in C:\Users\<USER>\source\repos\Thunee-fe\ThuneeAPI\ThuneeAPI.Infrastructure\Helpers\StoredProcedureHelper.cs:line 25
   at ThuneeAPI.Infrastructure.Data.BaseRepository.ExecuteStoredProcedureAsync[T](String storedProcedureName, Object parameters) in C:\Users\<USER>\source\repos\Thunee-fe\ThuneeAPI\ThuneeAPI.Infrastructure\Data\BaseRepository.cs:line 29
   at ThuneeAPI.Infrastructure.Data.Repositories.CompetitionRepository.ExecuteStoredProcedureAsync[T](String storedProcedureName, Object parameters) in C:\Users\<USER>\source\repos\Thunee-fe\ThuneeAPI\ThuneeAPI.Infrastructure\Data\Repositories\CompetitionRepository.cs:line 145
   at ThuneeAPI.Infrastructure.Services.CompetitionPhaseService.AdvanceCompetitionPhaseAsync(Guid competitionId, String newPhase) in C:\Users\<USER>\source\repos\Thunee-fe\ThuneeAPI\ThuneeAPI.Infrastructure\Services\CompetitionPhaseService.cs:line 104
2025-08-01 14:09:42.631 +02:00 [ERR] Error advancing competition "4860c19d-e3f3-4e2d-b359-275527461bd6" to phase Top32. Error: Stored procedure 'SP_AdvanceCompetitionToTop32' not found in resources
System.InvalidOperationException: Stored procedure 'SP_AdvanceCompetitionToTop32' not found in resources
   at ThuneeAPI.Infrastructure.Helpers.StoredProcedureHelper.GetStoredProcedure(String procedureName) in C:\Users\<USER>\source\repos\Thunee-fe\ThuneeAPI\ThuneeAPI.Infrastructure\Helpers\StoredProcedureHelper.cs:line 25
   at ThuneeAPI.Infrastructure.Data.BaseRepository.ExecuteStoredProcedureAsync[T](String storedProcedureName, Object parameters) in C:\Users\<USER>\source\repos\Thunee-fe\ThuneeAPI\ThuneeAPI.Infrastructure\Data\BaseRepository.cs:line 29
   at ThuneeAPI.Infrastructure.Data.Repositories.CompetitionRepository.ExecuteStoredProcedureAsync[T](String storedProcedureName, Object parameters) in C:\Users\<USER>\source\repos\Thunee-fe\ThuneeAPI\ThuneeAPI.Infrastructure\Data\Repositories\CompetitionRepository.cs:line 145
   at ThuneeAPI.Infrastructure.Services.CompetitionPhaseService.AdvanceCompetitionPhaseAsync(Guid competitionId, String newPhase) in C:\Users\<USER>\source\repos\Thunee-fe\ThuneeAPI\ThuneeAPI.Infrastructure\Services\CompetitionPhaseService.cs:line 104
   at ThuneeAPI.Controllers.CompetitionPhaseController.AdvancePhase(Guid competitionId, AdvancePhaseDto dto) in C:\Users\<USER>\source\repos\Thunee-fe\ThuneeAPI\ThuneeAPI\Controllers\CompetitionPhaseController.cs:line 34
2025-08-01 14:10:00.958 +02:00 [INF] Attempting to advance competition "4860c19d-e3f3-4e2d-b359-275527461bd6" to phase Top32
2025-08-01 14:10:00.963 +02:00 [INF] Advancing competition "4860c19d-e3f3-4e2d-b359-275527461bd6" to phase Top32
2025-08-01 14:10:01.406 +02:00 [INF] Executing SP_AdvanceCompetitionToTop32 for competition "4860c19d-e3f3-4e2d-b359-275527461bd6" to phase Top32
2025-08-01 14:10:01.411 +02:00 [ERR] Error advancing competition "4860c19d-e3f3-4e2d-b359-275527461bd6" to phase Top32
System.InvalidOperationException: Stored procedure 'SP_AdvanceCompetitionToTop32' not found in resources
   at ThuneeAPI.Infrastructure.Helpers.StoredProcedureHelper.GetStoredProcedure(String procedureName) in C:\Users\<USER>\source\repos\Thunee-fe\ThuneeAPI\ThuneeAPI.Infrastructure\Helpers\StoredProcedureHelper.cs:line 25
   at ThuneeAPI.Infrastructure.Data.BaseRepository.ExecuteStoredProcedureAsync[T](String storedProcedureName, Object parameters) in C:\Users\<USER>\source\repos\Thunee-fe\ThuneeAPI\ThuneeAPI.Infrastructure\Data\BaseRepository.cs:line 29
   at ThuneeAPI.Infrastructure.Data.Repositories.CompetitionRepository.ExecuteStoredProcedureAsync[T](String storedProcedureName, Object parameters) in C:\Users\<USER>\source\repos\Thunee-fe\ThuneeAPI\ThuneeAPI.Infrastructure\Data\Repositories\CompetitionRepository.cs:line 145
   at ThuneeAPI.Infrastructure.Services.CompetitionPhaseService.AdvanceCompetitionPhaseAsync(Guid competitionId, String newPhase) in C:\Users\<USER>\source\repos\Thunee-fe\ThuneeAPI\ThuneeAPI.Infrastructure\Services\CompetitionPhaseService.cs:line 104
2025-08-01 14:10:01.422 +02:00 [ERR] Error advancing competition "4860c19d-e3f3-4e2d-b359-275527461bd6" to phase Top32. Error: Stored procedure 'SP_AdvanceCompetitionToTop32' not found in resources
System.InvalidOperationException: Stored procedure 'SP_AdvanceCompetitionToTop32' not found in resources
   at ThuneeAPI.Infrastructure.Helpers.StoredProcedureHelper.GetStoredProcedure(String procedureName) in C:\Users\<USER>\source\repos\Thunee-fe\ThuneeAPI\ThuneeAPI.Infrastructure\Helpers\StoredProcedureHelper.cs:line 25
   at ThuneeAPI.Infrastructure.Data.BaseRepository.ExecuteStoredProcedureAsync[T](String storedProcedureName, Object parameters) in C:\Users\<USER>\source\repos\Thunee-fe\ThuneeAPI\ThuneeAPI.Infrastructure\Data\BaseRepository.cs:line 29
   at ThuneeAPI.Infrastructure.Data.Repositories.CompetitionRepository.ExecuteStoredProcedureAsync[T](String storedProcedureName, Object parameters) in C:\Users\<USER>\source\repos\Thunee-fe\ThuneeAPI\ThuneeAPI.Infrastructure\Data\Repositories\CompetitionRepository.cs:line 145
   at ThuneeAPI.Infrastructure.Services.CompetitionPhaseService.AdvanceCompetitionPhaseAsync(Guid competitionId, String newPhase) in C:\Users\<USER>\source\repos\Thunee-fe\ThuneeAPI\ThuneeAPI.Infrastructure\Services\CompetitionPhaseService.cs:line 104
   at ThuneeAPI.Controllers.CompetitionPhaseController.AdvancePhase(Guid competitionId, AdvancePhaseDto dto) in C:\Users\<USER>\source\repos\Thunee-fe\ThuneeAPI\ThuneeAPI\Controllers\CompetitionPhaseController.cs:line 34
2025-08-01 14:16:16.783 +02:00 [INF] Starting Thunee API Server
