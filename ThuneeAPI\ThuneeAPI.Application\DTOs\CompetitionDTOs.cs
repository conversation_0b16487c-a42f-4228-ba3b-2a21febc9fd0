namespace ThuneeAPI.Application.DTOs;

public class CompetitionDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public string Status { get; set; } = string.Empty;
    public int MaxTeams { get; set; }
    public int CurrentTeams { get; set; }
    public decimal EntryFee { get; set; }
    public PrizesDto Prizes { get; set; } = new();
    public decimal? TotalPrizePool { get; set; }
    public string? Rules { get; set; }
    public bool IsPublic { get; set; }
    public bool AllowSpectators { get; set; }
    public DateTime CreatedAt { get; set; }

    // Phase Management
    public string Phase { get; set; } 
    public DateTime? PhaseEndDate { get; set; }
}

public class PrizesDto
{
    public string? First { get; set; }
    public string? Second { get; set; }
    public string? Third { get; set; }
}

public class CreateCompetitionDto
{
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public int MaxTeams { get; set; } = 32;
    public decimal EntryFee { get; set; } = 0;
    public string? PrizeFirst { get; set; }
    public string? PrizeSecond { get; set; }
    public string? PrizeThird { get; set; }
    public decimal? TotalPrizePool { get; set; }
    public string? Rules { get; set; }
    public bool IsPublic { get; set; } = true;
    public bool AllowSpectators { get; set; } = true;
}

public class UpdateCompetitionDto
{
    public string? Name { get; set; }
    public string? Description { get; set; }
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public int? MaxTeams { get; set; }
    public decimal? EntryFee { get; set; }
    public string? PrizeFirst { get; set; }
    public string? PrizeSecond { get; set; }
    public string? PrizeThird { get; set; }
    public decimal? TotalPrizePool { get; set; }
    public string? Rules { get; set; }
    public bool? IsPublic { get; set; }
    public bool? AllowSpectators { get; set; }
}

public class CompetitionTeamDto
{
    public Guid Id { get; set; }
    public string TeamName { get; set; } = string.Empty;
    public PlayerDto Player1 { get; set; } = new();
    public PlayerDto? Player2 { get; set; } // Nullable until partner joins
    public string InviteCode { get; set; } = string.Empty;
    public int GamesPlayed { get; set; }
    public int Points { get; set; }
    public int BonusPoints { get; set; }
    public int MaxGames { get; set; }
    public bool IsComplete { get; set; }
    public DateTime RegisteredAt { get; set; }
    public DateTime? CompletedAt { get; set; }

    // Phase Management
    public string Phase { get; set; } = "Leaderboard";
    public bool IsEliminated { get; set; } = false;
    public bool AdvancedToNextPhase { get; set; } = false;
    public DateTime? PhaseEliminatedAt { get; set; }
}

// Knockout Competition DTOs
public class CompetitionPhaseLobbyDto
{
    public Guid Id { get; set; }
    public Guid CompetitionId { get; set; }
    public string Phase { get; set; } = string.Empty;
    public string LobbyCode { get; set; } = string.Empty;
    public DateTime? MatchScheduledAt { get; set; }
    public int BestOfGames { get; set; } = 3;
    public int RequiredWins { get; set; } = 2;
    public string MatchStatus { get; set; } = "Pending";
    public DateTime? NotificationSentAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public Guid CreatedByAdminId { get; set; }
    public string CreatedByAdminUsername { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }

    public List<CompetitionPhaseLobbyTeamDto> Teams { get; set; } = new();
    public List<GameSummaryDto> Games { get; set; } = new();
    public CompetitionPhaseLobbyTeamDto? Winner { get; set; }

    // Statistics
    public int GamesPlayed { get; set; }
    public int Team1Wins { get; set; }
    public int Team2Wins { get; set; }
}

public class CompetitionPhaseLobbyTeamDto
{
    public Guid Id { get; set; }
    public Guid LobbyId { get; set; }
    public Guid CompetitionTeamId { get; set; }
    public string TeamName { get; set; } = string.Empty;
    public bool IsWinner { get; set; }
    public DateTime? EliminatedAt { get; set; }

    // Player Information
    public Guid Player1Id { get; set; }
    public Guid? Player2Id { get; set; }
    public string Player1Name { get; set; } = string.Empty;
    public string? Player2Name { get; set; }
}

public class CreateCompetitionPhaseLobbyDto
{
    public Guid CompetitionId { get; set; }
    public string Phase { get; set; } = string.Empty;
    public List<Guid> TeamIds { get; set; } = new();
    public DateTime? MatchScheduledAt { get; set; }
    public int BestOfGames { get; set; } = 3;
    public int RequiredWins { get; set; } = 2;
    public bool SendNotification { get; set; } = true;
}

public class GameSummaryDto
{
    public Guid Id { get; set; }
    public string LobbyCode { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public int? WinnerTeam { get; set; }
    public string? WinnerTeamName { get; set; }
    public int Team1Score { get; set; }
    public int Team2Score { get; set; }
    public int Team1BallsWon { get; set; }
    public int Team2BallsWon { get; set; }
    public DateTime? StartedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
}

public class MatchResultDto
{
    public Guid LobbyId { get; set; }
    public bool IsMatchComplete { get; set; }
    public int Team1Wins { get; set; }
    public int Team2Wins { get; set; }
    public int RequiredWins { get; set; }
    public int? WinnerTeamNumber { get; set; }
    public Guid? WinnerTeamId { get; set; }
    public int TotalGames { get; set; }
}

public class MatchSchedulingDto
{
    public Guid LobbyId { get; set; }
    public DateTime ScheduledAt { get; set; }
    public string? CustomMessage { get; set; }
    public bool SendImmediateNotification { get; set; } = true;
}

public class AdminLobbyViewDto
{
    public Guid LobbyId { get; set; }
    public string LobbyCode { get; set; } = string.Empty;
    public string Phase { get; set; } = string.Empty;
    public string MatchStatus { get; set; } = string.Empty;
    public DateTime? MatchScheduledAt { get; set; }
    public int BestOfGames { get; set; }
    public int RequiredWins { get; set; }
    public DateTime? CompletedAt { get; set; }
    public DateTime? NotificationSentAt { get; set; }
    public string CreatedByAdmin { get; set; } = string.Empty;

    // Team 1 Info
    public Guid Team1Id { get; set; }
    public string Team1Name { get; set; } = string.Empty;
    public string Team1Player1 { get; set; } = string.Empty;
    public string? Team1Player2 { get; set; }

    // Team 2 Info
    public Guid Team2Id { get; set; }
    public string Team2Name { get; set; } = string.Empty;
    public string Team2Player1 { get; set; } = string.Empty;
    public string? Team2Player2 { get; set; }

    // Match Statistics
    public int GamesPlayed { get; set; }
    public int Team1Wins { get; set; }
    public int Team2Wins { get; set; }
    public string? WinnerTeamName { get; set; }
    public Guid? WinnerTeamId { get; set; }
}

// Additional DTOs for controller endpoints
public class AdvancePhaseDto
{
    public string NewPhase { get; set; } = string.Empty;
}

public class CreatePhaseLobbyDto
{
    public List<Guid> TeamIds { get; set; } = new();
    public DateTime? MatchScheduledAt { get; set; }
    public int BestOfGames { get; set; } = 3;
    public int RequiredWins { get; set; } = 2;
    public bool SendNotification { get; set; } = true;
}



public class CreateMultipleLobbiesDto
{
    public List<TeamPairingDto> TeamPairings { get; set; } = new();
    public DateTime? MatchScheduledAt { get; set; }
    public int BestOfGames { get; set; } = 3;
    public int RequiredWins { get; set; } = 2;
    public bool SendNotification { get; set; } = true;
}

public class TeamPairingDto
{
    public List<Guid> TeamIds { get; set; } = new();
    public string? Notes { get; set; }
}

public class CreateCompetitionTeamDto
{
    public Guid CompetitionId { get; set; }
    public string TeamName { get; set; } = string.Empty;
}

public class JoinCompetitionTeamDto
{
    public string InviteCode { get; set; } = string.Empty;
}

public class JoinCompetitionDto
{
    public Guid CompetitionId { get; set; }
    public string TeamName { get; set; } = string.Empty;
    public Guid Player2Id { get; set; }
}

public class LeaderboardEntryDto
{
    public Guid Id { get; set; }
    public Guid PlayerId { get; set; }
    public string PlayerName { get; set; } = string.Empty;
    public int Score { get; set; }
    public int Rank { get; set; }
    public int GamesPlayed { get; set; }
    public int GamesWon { get; set; }
    public double WinRate { get; set; }
}

public class LeaderboardResponseDto
{
    public List<LeaderboardEntryDto> Data { get; set; } = new();
    public PaginationDto Pagination { get; set; } = new();
    public FilterDto Filters { get; set; } = new();
}

public class PaginationDto
{
    public int CurrentPage { get; set; }
    public int TotalPages { get; set; }
    public int TotalItems { get; set; }
    public int ItemsPerPage { get; set; }
}

public class FilterDto
{
    public string TimeFrame { get; set; } = string.Empty;
    public string SortBy { get; set; } = string.Empty;
}

public class CompetitionStatusDto
{
    public Guid CompetitionId { get; set; }
    public string CompetitionName { get; set; } = string.Empty;
    public CompetitionTeamDto? Team { get; set; }
    public bool HasTeam { get; set; }
    public bool CanJoin { get; set; }
    public bool CanResume { get; set; }
    public string Status { get; set; } = string.Empty; // waiting_for_partner, ready_to_play, in_progress, completed
}

public class CompetitionLeaderboardEntryDto
{
    public Guid TeamId { get; set; }
    public string TeamName { get; set; } = string.Empty;
    public PlayerDto Player1 { get; set; } = new();
    public PlayerDto Player2 { get; set; } = new();
    public int Points { get; set; }
    public int BonusPoints { get; set; }
    public int TotalPoints => Points + BonusPoints;
    public int GamesPlayed { get; set; }
    public int GamesWon { get; set; }
    public double WinRate => GamesPlayed > 0 ? (double)GamesWon / GamesPlayed * 100 : 0;
    public int Rank { get; set; }
}

public class CompetitionLeaderboardResponseDto
{
    public List<CompetitionLeaderboardEntryDto> Teams { get; set; } = new();
    public PaginationDto Pagination { get; set; } = new();
}

public class CompetitionGameResultDto
{
    public Guid GameId { get; set; }
    public Guid CompetitionId { get; set; }
    public Guid Team1Id { get; set; }
    public Guid Team2Id { get; set; }
    public int Team1Score { get; set; }
    public int Team2Score { get; set; }
    public int Team1BallsWon { get; set; }
    public int Team2BallsWon { get; set; }
    public int WinnerTeam { get; set; } // 1 or 2
}



public class CompetitionTeamPhaseStatsDto
{
    public Guid Id { get; set; }
    public Guid CompetitionTeamId { get; set; }
    public string Phase { get; set; } = string.Empty;
    public int Points { get; set; }
    public int BonusPoints { get; set; }
    public int GamesPlayed { get; set; }
    public int BallsWon { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public class BracketDto
{
    public string Phase { get; set; } = string.Empty;
    public List<BracketMatchDto> Matches { get; set; } = new();
}

public class BracketMatchDto
{
    public Guid LobbyId { get; set; }
    public string LobbyCode { get; set; } = string.Empty;
    public CompetitionTeamDto? Team1 { get; set; }
    public CompetitionTeamDto? Team2 { get; set; }
    public CompetitionTeamDto? Winner { get; set; }
    public bool IsCompleted { get; set; }
    public DateTime? CompletedAt { get; set; }
}
