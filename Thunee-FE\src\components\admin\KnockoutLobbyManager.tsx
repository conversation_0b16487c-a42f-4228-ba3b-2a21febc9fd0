"use client";
import { useState } from "react";
import { Plus, <PERSON>, Clock, Trophy, Copy, Calendar, Bell, CheckCircle, AlertCircle, Eye, Loader2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useAuthStore } from "@/store/authStore";
import { apiService } from "@/services/api";
import { apiBaseUrl } from "@/config/env";
import { toast } from "sonner";

interface Competition {
  id: string;
  name: string;
  phase: string;
  status: string;
}

interface CompetitionTeam {
  id: string;
  teamName: string;
  player1: { id: string; username: string };
  player2?: { id: string; username: string };
  phase: string;
  isEliminated: boolean;
}

interface AdminLobbyView {
  lobbyId: string;
  lobbyCode: string;
  phase: string;
  matchStatus: string;
  matchScheduledAt?: string;
  bestOfGames: number;
  requiredWins: number;
  completedAt?: string;
  notificationSentAt?: string;
  createdByAdmin: string;
  team1Id: string;
  team1Name: string;
  team1Player1: string;
  team1Player2?: string;
  team2Id: string;
  team2Name: string;
  team2Player1: string;
  team2Player2?: string;
  gamesPlayed: number;
  team1Wins: number;
  team2Wins: number;
  winnerTeamName?: string;
  winnerTeamId?: string;
}

interface KnockoutLobbyManagerProps {
  competitionId: string;
  competition: Competition;
  teams: CompetitionTeam[];
  adminLobbies: AdminLobbyView[];
  onLobbiesUpdated: () => void;
}

export default function KnockoutLobbyManager({
  competitionId,
  competition,
  teams,
  adminLobbies,
  onLobbiesUpdated
}: KnockoutLobbyManagerProps) {
  const { user } = useAuthStore();
  const [isCreating, setIsCreating] = useState(false);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [selectedTeam1, setSelectedTeam1] = useState<string>("");
  const [selectedTeam2, setSelectedTeam2] = useState<string>("");
  const [bestOfGames, setBestOfGames] = useState<number>(3);
  const [scheduledDate, setScheduledDate] = useState<string>("");
  const [scheduledTime, setScheduledTime] = useState<string>("");

  // Quick Create All Lobbies state
  const [showQuickCreate, setShowQuickCreate] = useState(false);
  const [quickCreateStartDate, setQuickCreateStartDate] = useState("");
  const [quickCreateStartTime, setQuickCreateStartTime] = useState("18:00");
  const [quickCreateInterval, setQuickCreateInterval] = useState(60);
  const [quickCreateBestOf, setQuickCreateBestOf] = useState(3);
  const [isQuickCreating, setIsQuickCreating] = useState(false);
  const [previewMatches, setPreviewMatches] = useState<any[]>([]);
  const [showPreview, setShowPreview] = useState(false);

  const availableTeams = teams.filter(team =>
    !team.isEliminated &&
    team.phase === competition.phase &&
    !adminLobbies.some(lobby =>
      lobby.team1Id === team.id || lobby.team2Id === team.id
    )
  );

  // Generate preview matches for quick create
  const generatePreviewMatches = () => {
    if (!quickCreateStartDate || !quickCreateStartTime) {
      toast.error("Please select start date and time");
      return;
    }

    const sortedTeams = [...availableTeams]; // Use teams as they are for now

    const matches = [];
    const startDateTime = new Date(`${quickCreateStartDate}T${quickCreateStartTime}`);

    for (let i = 0; i < sortedTeams.length / 2; i++) {
      const team1 = sortedTeams[i];
      const team2 = sortedTeams[sortedTeams.length - 1 - i];

      if (team1 && team2) {
        const matchTime = new Date(startDateTime.getTime() + (i * quickCreateInterval * 60000));
        matches.push({
          matchNumber: i + 1,
          team1,
          team2,
          scheduledAt: matchTime,
          bestOfGames: quickCreateBestOf
        });
      }
    }

    setPreviewMatches(matches);
    setShowPreview(true);
  };

  // Create all lobbies from preview
  const handleQuickCreateAll = async () => {
    if (previewMatches.length === 0) {
      toast.error("No matches to create");
      return;
    }

    setIsQuickCreating(true);
    try {
      const response = await fetch(`${apiBaseUrl}/competitions/${competitionId}/phases/${competition.phase}/matches/create-all`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiService.getToken()}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          startTime: previewMatches[0].scheduledAt.toISOString(),
          matchIntervalMinutes: quickCreateInterval,
          bestOfGames: quickCreateBestOf
        })
      });

      if (!response.ok) {
        throw new Error('Failed to create matches');
      }

      const result = await response.json();
      toast.success(`Successfully created ${result.data.length} knockout matches!`);

      setShowQuickCreate(false);
      setShowPreview(false);
      setPreviewMatches([]);
      onLobbiesUpdated();

    } catch (error) {
      console.error("Error creating matches:", error);
      toast.error("Failed to create matches");
    } finally {
      setIsQuickCreating(false);
    }
  };

  const handleCreateLobby = async () => {
    if (!selectedTeam1 || !selectedTeam2 || selectedTeam1 === selectedTeam2) {
      toast.error("Please select two different teams");
      return;
    }

    try {
      setIsCreating(true);

      const matchScheduledAt = scheduledDate && scheduledTime 
        ? new Date(`${scheduledDate}T${scheduledTime}`).toISOString()
        : null;

      const requiredWins = Math.ceil(bestOfGames / 2);

      const response = await fetch(`${apiBaseUrl}/competitions/${competitionId}/phases/${competition.phase}/lobbies`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiService.getToken()}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          teamIds: [selectedTeam1, selectedTeam2],
          matchScheduledAt,
          bestOfGames,
          requiredWins,
          sendNotification: true
        })
      });

      if (response.ok) {
        const data = await response.json();
        toast.success(`Lobby created successfully! Code: ${data.data.lobbyCode}`);
        setShowCreateDialog(false);
        setSelectedTeam1("");
        setSelectedTeam2("");
        setScheduledDate("");
        setScheduledTime("");
        onLobbiesUpdated();
      } else {
        const error = await response.json();
        toast.error(error.message || "Failed to create lobby");
      }
    } catch (error) {
      console.error("Error creating lobby:", error);
      toast.error("Failed to create lobby");
    } finally {
      setIsCreating(false);
    }
  };

  const handleCopyLobbyCode = (lobbyCode: string) => {
    navigator.clipboard.writeText(lobbyCode);
    toast.success("Lobby code copied to clipboard!");
  };

  const handleSendNotification = async (lobbyId: string) => {
    try {
      const response = await fetch(`${apiBaseUrl}/competitions/${competitionId}/phases/lobbies/${lobbyId}/notify`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiService.getToken()}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        toast.success("Notification sent successfully!");
        onLobbiesUpdated();
      } else {
        toast.error("Failed to send notification");
      }
    } catch (error) {
      console.error("Error sending notification:", error);
      toast.error("Failed to send notification");
    }
  };

  const getMatchStatusColor = (status: string) => {
    switch (status) {
      case "Pending": return "bg-yellow-500/20 text-yellow-400 border-yellow-500/30";
      case "Scheduled": return "bg-blue-500/20 text-blue-400 border-blue-500/30";
      case "InProgress": return "bg-green-500/20 text-green-400 border-green-500/30";
      case "Completed": return "bg-purple-500/20 text-purple-400 border-purple-500/30";
      default: return "bg-gray-500/20 text-gray-400 border-gray-500/30";
    }
  };

  const getMatchProgress = (lobby: AdminLobbyView) => {
    if (lobby.matchStatus === "Completed") {
      return `Winner: ${lobby.winnerTeamName}`;
    }
    return `${lobby.team1Wins}-${lobby.team2Wins} (First to ${lobby.requiredWins})`;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-[#E1C760]">Knockout Lobbies</h2>
          <p className="text-gray-400">Manage {competition.phase} phase lobbies and matches</p>
        </div>
        
        <div className="flex gap-2 items-center">
          {/* Quick Create All Lobbies Button */}
          <Button
            onClick={() => {
              // Set default date to tomorrow
              const tomorrow = new Date();
              tomorrow.setDate(tomorrow.getDate() + 1);
              setQuickCreateStartDate(tomorrow.toISOString().split('T')[0]);
              setShowQuickCreate(true);
            }}
            className="bg-green-600 text-white hover:bg-green-700"
            disabled={availableTeams.length < 4 || adminLobbies.length > 0}
          >
            <Trophy className="h-4 w-4 mr-2" />
            Quick Create All Matches
          </Button>

          {(availableTeams.length < 4 || adminLobbies.length > 0) && (
            <span className="text-xs text-gray-500">
              {adminLobbies.length > 0
                ? "Clear existing lobbies first"
                : `Need at least 4 teams (${availableTeams.length} available)`
              }
            </span>
          )}
        </div>

        <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
            <DialogTrigger asChild>
              <Button
                className="bg-[#E1C760] text-black hover:bg-[#E1C760]/80"
                disabled={availableTeams.length < 2}
              >
                <Plus className="h-4 w-4 mr-2" />
                Create Single Lobby
              </Button>
            </DialogTrigger>
          <DialogContent className="bg-[#1A1A1A] border-[#333333] text-white">
            <DialogHeader>
              <DialogTitle className="text-[#E1C760]">Create Knockout Lobby</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="team1">Team 1</Label>
                  <Select value={selectedTeam1} onValueChange={setSelectedTeam1}>
                    <SelectTrigger className="bg-[#2A2A2A] border-[#444444]">
                      <SelectValue placeholder="Select Team 1" />
                    </SelectTrigger>
                    <SelectContent className="bg-[#2A2A2A] border-[#444444]">
                      {availableTeams.map((team) => (
                        <SelectItem key={team.id} value={team.id}>
                          {team.teamName} ({team.player1.username}
                          {team.player2 && ` & ${team.player2.username}`})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div>
                  <Label htmlFor="team2">Team 2</Label>
                  <Select value={selectedTeam2} onValueChange={setSelectedTeam2}>
                    <SelectTrigger className="bg-[#2A2A2A] border-[#444444]">
                      <SelectValue placeholder="Select Team 2" />
                    </SelectTrigger>
                    <SelectContent className="bg-[#2A2A2A] border-[#444444]">
                      {availableTeams.filter(team => team.id !== selectedTeam1).map((team) => (
                        <SelectItem key={team.id} value={team.id}>
                          {team.teamName} ({team.player1.username}
                          {team.player2 && ` & ${team.player2.username}`})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <Label htmlFor="bestOf">Match Format</Label>
                <Select value={bestOfGames.toString()} onValueChange={(value) => setBestOfGames(parseInt(value))}>
                  <SelectTrigger className="bg-[#2A2A2A] border-[#444444]">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="bg-[#2A2A2A] border-[#444444]">
                    <SelectItem value="1">Single Game</SelectItem>
                    <SelectItem value="3">Best of 3</SelectItem>
                    <SelectItem value="5">Best of 5</SelectItem>
                    <SelectItem value="7">Best of 7</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="date">Scheduled Date (Optional)</Label>
                  <Input
                    id="date"
                    type="date"
                    value={scheduledDate}
                    onChange={(e) => setScheduledDate(e.target.value)}
                    className="bg-[#2A2A2A] border-[#444444]"
                  />
                </div>
                
                <div>
                  <Label htmlFor="time">Scheduled Time (Optional)</Label>
                  <Input
                    id="time"
                    type="time"
                    value={scheduledTime}
                    onChange={(e) => setScheduledTime(e.target.value)}
                    className="bg-[#2A2A2A] border-[#444444]"
                  />
                </div>
              </div>

              <Button
                onClick={handleCreateLobby}
                disabled={isCreating || !selectedTeam1 || !selectedTeam2}
                className="w-full bg-[#E1C760] text-black hover:bg-[#E1C760]/80"
              >
                {isCreating ? "Creating..." : "Create Lobby"}
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Lobbies Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {adminLobbies.map((lobby) => (
          <Card key={lobby.lobbyId} className="bg-black/50 border-[#E1C760]/30">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Trophy className="h-5 w-5 text-[#E1C760]" />
                  <CardTitle className="text-[#E1C760]">
                    {lobby.team1Name} vs {lobby.team2Name}
                  </CardTitle>
                </div>
                <Badge className={getMatchStatusColor(lobby.matchStatus)}>
                  {lobby.matchStatus}
                </Badge>
              </div>
              <CardDescription className="text-gray-400">
                Best of {lobby.bestOfGames} • {getMatchProgress(lobby)}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Lobby Code */}
              <div className="flex items-center justify-between p-3 bg-gray-800/50 rounded-lg">
                <div>
                  <p className="text-sm text-gray-400">Lobby Code</p>
                  <p className="text-lg font-mono text-[#E1C760]">{lobby.lobbyCode}</p>
                </div>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => handleCopyLobbyCode(lobby.lobbyCode)}
                  className="text-[#E1C760] hover:bg-[#E1C760]/10"
                >
                  <Copy className="h-4 w-4" />
                </Button>
              </div>

              {/* Teams */}
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center">
                  <p className="font-medium text-white">{lobby.team1Name}</p>
                  <p className="text-sm text-gray-400">{lobby.team1Player1}</p>
                  {lobby.team1Player2 && (
                    <p className="text-sm text-gray-400">{lobby.team1Player2}</p>
                  )}
                  <p className="text-lg font-bold text-[#E1C760] mt-1">{lobby.team1Wins}</p>
                </div>
                <div className="text-center">
                  <p className="font-medium text-white">{lobby.team2Name}</p>
                  <p className="text-sm text-gray-400">{lobby.team2Player1}</p>
                  {lobby.team2Player2 && (
                    <p className="text-sm text-gray-400">{lobby.team2Player2}</p>
                  )}
                  <p className="text-lg font-bold text-[#E1C760] mt-1">{lobby.team2Wins}</p>
                </div>
              </div>

              {/* Match Info */}
              <div className="flex items-center justify-between text-sm">
                <div className="flex items-center gap-2 text-gray-400">
                  <Users className="h-4 w-4" />
                  <span>{lobby.gamesPlayed} games played</span>
                </div>
                {lobby.matchScheduledAt && (
                  <div className="flex items-center gap-2 text-gray-400">
                    <Clock className="h-4 w-4" />
                    <span>{new Date(lobby.matchScheduledAt).toLocaleDateString()}</span>
                  </div>
                )}
              </div>

              {/* Actions */}
              <div className="flex gap-2">
                {!lobby.notificationSentAt && (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleSendNotification(lobby.lobbyId)}
                    className="flex-1 border-[#E1C760] text-[#E1C760] hover:bg-[#E1C760]/10"
                  >
                    <Bell className="h-4 w-4 mr-2" />
                    Send Notification
                  </Button>
                )}
                {lobby.matchStatus === "Completed" && (
                  <div className="flex items-center gap-2 text-green-400">
                    <CheckCircle className="h-4 w-4" />
                    <span className="text-sm">Complete</span>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {adminLobbies.length === 0 && (
        <Card className="bg-black/50 border-[#E1C760]/30">
          <CardContent className="text-center py-12">
            <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-white mb-2">No Lobbies Created</h3>
            <p className="text-gray-400 mb-4">
              Create knockout lobbies to start the {competition.phase} phase matches.
            </p>
            {availableTeams.length >= 2 ? (
              <Button
                onClick={() => setShowCreateDialog(true)}
                className="bg-[#E1C760] text-black hover:bg-[#E1C760]/80"
              >
                <Plus className="h-4 w-4 mr-2" />
                Create First Lobby
              </Button>
            ) : (
              <p className="text-sm text-red-400">
                Not enough available teams to create lobbies.
              </p>
            )}
          </CardContent>
        </Card>
      )}

      {/* Quick Create All Matches Dialog */}
      <Dialog open={showQuickCreate} onOpenChange={setShowQuickCreate}>
        <DialogContent className="bg-[#1A1A1A] border-[#333333] text-white max-w-4xl">
          <DialogHeader>
            <DialogTitle className="text-[#E1C760]">Quick Create All Knockout Matches</DialogTitle>
            <p className="text-gray-400">
              Create all {Math.floor(availableTeams.length / 2)} matches for {availableTeams.length} teams
            </p>
          </DialogHeader>

          <div className="space-y-6">
            {/* Configuration */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="startDate">Start Date</Label>
                <Input
                  id="startDate"
                  type="date"
                  value={quickCreateStartDate}
                  onChange={(e) => setQuickCreateStartDate(e.target.value)}
                  className="bg-[#2A2A2A] border-[#444444]"
                  min={new Date().toISOString().split('T')[0]}
                />
              </div>

              <div>
                <Label htmlFor="startTime">Start Time</Label>
                <Input
                  id="startTime"
                  type="time"
                  value={quickCreateStartTime}
                  onChange={(e) => setQuickCreateStartTime(e.target.value)}
                  className="bg-[#2A2A2A] border-[#444444]"
                />
              </div>

              <div>
                <Label htmlFor="interval">Match Interval (minutes)</Label>
                <Input
                  id="interval"
                  type="number"
                  value={quickCreateInterval}
                  onChange={(e) => setQuickCreateInterval(parseInt(e.target.value))}
                  className="bg-[#2A2A2A] border-[#444444]"
                  min="30"
                  max="180"
                />
              </div>

              <div>
                <Label htmlFor="bestOf">Match Format</Label>
                <Select value={quickCreateBestOf.toString()} onValueChange={(value) => setQuickCreateBestOf(parseInt(value))}>
                  <SelectTrigger className="bg-[#2A2A2A] border-[#444444]">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="bg-[#2A2A2A] border-[#444444]">
                    <SelectItem value="1">Single Game (First to 1)</SelectItem>
                    <SelectItem value="3">Best of 3 (First to 2)</SelectItem>
                    <SelectItem value="5">Best of 5 (First to 3)</SelectItem>
                    <SelectItem value="7">Best of 7 (First to 4)</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-xs text-gray-500 mt-1">
                  Each match is between the same two teams until one reaches the required wins
                </p>
              </div>
            </div>

            {/* Preview Button */}
            <div className="flex justify-center">
              <Button
                onClick={generatePreviewMatches}
                className="bg-blue-600 text-white hover:bg-blue-700"
                disabled={!quickCreateStartDate || !quickCreateStartTime}
              >
                <Eye className="h-4 w-4 mr-2" />
                Preview Matches
              </Button>
            </div>

            {/* Preview Matches */}
            {showPreview && previewMatches.length > 0 && (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-[#E1C760]">Match Preview</h3>
                <div className="max-h-96 overflow-y-auto space-y-2">
                  {previewMatches.map((match, index) => (
                    <div key={index} className="bg-[#2A2A2A] p-4 rounded-lg border border-[#444444]">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                          <span className="text-[#E1C760] font-semibold">Match {match.matchNumber}</span>
                          <span className="text-white">
                            {match.team1.teamName} vs {match.team2.teamName}
                          </span>
                        </div>
                        <div className="text-right">
                          <div className="text-sm text-gray-400">
                            {match.scheduledAt.toLocaleDateString()} at {match.scheduledAt.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}
                          </div>
                          <div className="text-xs text-gray-500">
                            Best of {match.bestOfGames} (First to {Math.ceil(match.bestOfGames / 2)})
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Create All Button */}
                <div className="flex justify-end space-x-2">
                  <Button
                    onClick={() => setShowPreview(false)}
                    variant="outline"
                    className="border-[#444444] text-gray-400 hover:bg-[#2A2A2A]"
                  >
                    Back to Edit
                  </Button>
                  <Button
                    onClick={handleQuickCreateAll}
                    className="bg-green-600 text-white hover:bg-green-700"
                    disabled={isQuickCreating}
                  >
                    {isQuickCreating ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Creating...
                      </>
                    ) : (
                      <>
                        <Trophy className="h-4 w-4 mr-2" />
                        Create All {previewMatches.length} Matches
                      </>
                    )}
                  </Button>
                </div>
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
