2025-08-04 14:15:21.043 +02:00 [INF] Starting Thunee API Server
2025-08-04 14:15:27.348 +02:00 [INF] Creating all matches for competition "4860c19d-e3f3-4e2d-b359-275527461bd6" phase Top32
2025-08-04 14:15:27.357 +02:00 [INF] Executing SP_CreateAllPhaseMatches for Top32 starting at "2025-08-05T13:00:00.0000000Z"
2025-08-04 14:15:27.922 +02:00 [ERR] Error creating all phase matches for competition "4860c19d-e3f3-4e2d-b359-275527461bd6" phase Top32
Microsoft.Data.SqlClient.SqlException (0x80131904): Procedure or function 'SP_CreateAllPhaseMatches' expects parameter '@AdminId', which was not supplied.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__195_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Dapper.SqlMapper.QueryAsync[T](IDbConnection cnn, Type effectiveType, CommandDefinition command) in /_/Dapper/SqlMapper.Async.cs:line 434
   at ThuneeAPI.Infrastructure.Data.BaseRepository.ExecuteStoredProcedureAsync[T](String storedProcedureName, Object parameters) in C:\Users\<USER>\source\repos\Thunee-fe\ThuneeAPI\ThuneeAPI.Infrastructure\Data\BaseRepository.cs:line 30
   at ThuneeAPI.Infrastructure.Data.Repositories.CompetitionRepository.ExecuteStoredProcedureAsync[T](String storedProcedureName, Object parameters) in C:\Users\<USER>\source\repos\Thunee-fe\ThuneeAPI\ThuneeAPI.Infrastructure\Data\Repositories\CompetitionRepository.cs:line 145
   at ThuneeAPI.Infrastructure.Services.CompetitionPhaseService.CreateAllPhaseMatchesAsync(Guid competitionId, String phase, DateTime startTime, Int32 matchIntervalMinutes, Int32 bestOfGames) in C:\Users\<USER>\source\repos\Thunee-fe\ThuneeAPI\ThuneeAPI.Infrastructure\Services\CompetitionPhaseService.cs:line 396
ClientConnectionId:d05a76c0-d114-4e36-95b6-720d51a2dfee
Error Number:201,State:4,Class:16
2025-08-04 14:15:28.050 +02:00 [ERR] Error creating all phase matches for competition "4860c19d-e3f3-4e2d-b359-275527461bd6" phase Top32
Microsoft.Data.SqlClient.SqlException (0x80131904): Procedure or function 'SP_CreateAllPhaseMatches' expects parameter '@AdminId', which was not supplied.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__195_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Dapper.SqlMapper.QueryAsync[T](IDbConnection cnn, Type effectiveType, CommandDefinition command) in /_/Dapper/SqlMapper.Async.cs:line 434
   at ThuneeAPI.Infrastructure.Data.BaseRepository.ExecuteStoredProcedureAsync[T](String storedProcedureName, Object parameters) in C:\Users\<USER>\source\repos\Thunee-fe\ThuneeAPI\ThuneeAPI.Infrastructure\Data\BaseRepository.cs:line 30
   at ThuneeAPI.Infrastructure.Data.Repositories.CompetitionRepository.ExecuteStoredProcedureAsync[T](String storedProcedureName, Object parameters) in C:\Users\<USER>\source\repos\Thunee-fe\ThuneeAPI\ThuneeAPI.Infrastructure\Data\Repositories\CompetitionRepository.cs:line 145
   at ThuneeAPI.Infrastructure.Services.CompetitionPhaseService.CreateAllPhaseMatchesAsync(Guid competitionId, String phase, DateTime startTime, Int32 matchIntervalMinutes, Int32 bestOfGames) in C:\Users\<USER>\source\repos\Thunee-fe\ThuneeAPI\ThuneeAPI.Infrastructure\Services\CompetitionPhaseService.cs:line 396
   at ThuneeAPI.Controllers.CompetitionPhaseController.CreateAllPhaseMatches(Guid competitionId, String phase, CreateAllPhaseMatchesDto dto) in C:\Users\<USER>\source\repos\Thunee-fe\ThuneeAPI\ThuneeAPI\Controllers\CompetitionPhaseController.cs:line 439
ClientConnectionId:d05a76c0-d114-4e36-95b6-720d51a2dfee
Error Number:201,State:4,Class:16
