using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ThuneeAPI.Application.DTOs;
using ThuneeAPI.Application.Interfaces;
using System.Security.Claims;

namespace ThuneeAPI.Controllers;

[ApiController]
[Route("api/competitions/{competitionId}/phases")]
public class CompetitionPhaseController : ControllerBase
{
    private readonly ICompetitionPhaseService _phaseService;
    private readonly ILogger<CompetitionPhaseController> _logger;

    public CompetitionPhaseController(
        ICompetitionPhaseService phaseService,
        ILogger<CompetitionPhaseController> logger)
    {
        _phaseService = phaseService;
        _logger = logger;
    }

    /// <summary>
    /// Advance competition to next phase (Admin only)
    /// </summary>
    [HttpPost("advance")]
    public async Task<ActionResult<CompetitionDto>> AdvancePhase(Guid competitionId, [FromBody] AdvancePhaseDto dto)
    {
        try
        {
            _logger.LogInformation("Attempting to advance competition {CompetitionId} to phase {Phase}", competitionId, dto.NewPhase);
            
            var competition = await _phaseService.AdvanceCompetitionPhaseAsync(competitionId, dto.NewPhase);
            
            _logger.LogInformation("Competition {CompetitionId} successfully advanced to phase {Phase}", competitionId, dto.NewPhase);
            
            return Ok(new
            {
                success = true,
                data = competition,
                message = $"Competition advanced to {dto.NewPhase} phase"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error advancing competition {CompetitionId} to phase {Phase}. Error: {ErrorMessage}", 
                competitionId, dto.NewPhase, ex.Message);
            
            return StatusCode(500, new 
            { 
                message = "Failed to advance phase", 
                error = ex.Message,
                details = ex.InnerException?.Message
            });
        }
    }

    /// <summary>
    /// Get teams eligible for next phase
    /// </summary>
    [HttpGet("eligible-teams")]
    public async Task<ActionResult<List<CompetitionTeamDto>>> GetEligibleTeams(Guid competitionId)
    {
        try
        {
            var teams = await _phaseService.GetEligibleTeamsForNextPhaseAsync(competitionId);
            return Ok(new { success = true, data = teams });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting eligible teams for competition {CompetitionId}", competitionId);
            return StatusCode(500, new { message = "Failed to get eligible teams", error = ex.Message });
        }
    }

    /// <summary>
    /// Get teams for current phase
    /// </summary>
    [HttpGet("teams")]
    public async Task<ActionResult<List<CompetitionTeamDto>>> GetTeamsForPhase(Guid competitionId, [FromQuery] string phase)
    {
        try
        {
            var teams = await _phaseService.GetTeamsForPhaseAsync(competitionId, phase);
            return Ok(new { success = true, data = teams });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting teams for competition {CompetitionId} phase {Phase}", competitionId, phase);
            return StatusCode(500, new { message = "Failed to get teams for phase", error = ex.Message });
        }
    }

    /// <summary>
    /// Create a knockout phase lobby
    /// </summary>
    [HttpPost("lobbies")]
    public async Task<ActionResult<CompetitionPhaseLobbyDto>> CreatePhaseLobby(Guid competitionId, [FromBody] CreateCompetitionPhaseLobbyDto dto)
    {
        try
        {
            var adminId = Guid.Parse(User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? Guid.Empty.ToString());
            dto.CompetitionId = competitionId;

            var lobby = await _phaseService.CreatePhaseLobbyAsync(dto, adminId);
            return Ok(new { success = true, data = lobby });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating phase lobby for competition {CompetitionId}", competitionId);
            return StatusCode(500, new { message = "Failed to create phase lobby", error = ex.Message });
        }
    }

    /// <summary>
    /// Get all lobbies for a specific phase
    /// </summary>
    [HttpGet("{phase}/lobbies")]
    public async Task<ActionResult<List<CompetitionPhaseLobbyDto>>> GetPhaseLobbies(Guid competitionId, string phase)
    {
        try
        {
            var lobbies = await _phaseService.GetPhaseLobbiesAsync(competitionId, phase);
            return Ok(new { success = true, data = lobbies });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting phase lobbies for competition {CompetitionId} phase {Phase}", competitionId, phase);
            return StatusCode(500, new { message = "Failed to get phase lobbies", error = ex.Message });
        }
    }

    /// <summary>
    /// Get lobby details by lobby code
    /// </summary>
    [HttpGet("lobbies/{lobbyCode}")]
    public async Task<ActionResult<CompetitionPhaseLobbyDto>> GetLobbyByCode(string lobbyCode)
    {
        try
        {
            var lobby = await _phaseService.GetPhaseLobbyByCodeAsync(lobbyCode);
            return Ok(new { success = true, data = lobby });
        }
        catch (ArgumentException ex)
        {
            return NotFound(new { success = false, message = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting lobby by code {LobbyCode}", lobbyCode);
            return StatusCode(500, new { message = "Failed to get lobby details", error = ex.Message });
        }
    }

    /// <summary>
    /// Get lobby details by lobby ID
    /// </summary>
    [HttpGet("lobbies/id/{lobbyId}")]
    public async Task<ActionResult<CompetitionPhaseLobbyDto>> GetLobbyById(Guid lobbyId)
    {
        try
        {
            var lobby = await _phaseService.GetPhaseLobbyByIdAsync(lobbyId);
            return Ok(new { success = true, data = lobby });
        }
        catch (ArgumentException ex)
        {
            return NotFound(new { success = false, message = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting lobby by ID {LobbyId}", lobbyId);
            return StatusCode(500, new { message = "Failed to get lobby details", error = ex.Message });
        }
    }

    /// <summary>
    /// Schedule a match for a lobby
    /// </summary>
    [HttpPut("lobbies/{lobbyId}/schedule")]
    public async Task<ActionResult> ScheduleMatch(Guid lobbyId, [FromBody] MatchSchedulingDto dto)
    {
        try
        {
            await _phaseService.ScheduleMatchAsync(lobbyId, dto.ScheduledAt, dto.CustomMessage);

            if (dto.SendImmediateNotification)
            {
                await _phaseService.SendMatchNotificationAsync(lobbyId, "MatchScheduled");
            }

            return Ok(new { success = true, message = "Match scheduled successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error scheduling match for lobby {LobbyId}", lobbyId);
            return StatusCode(500, new { message = "Failed to schedule match", error = ex.Message });
        }
    }

    /// <summary>
    /// Process game result for knockout match
    /// </summary>
    [HttpPost("lobbies/{lobbyId}/games/{gameId}/result")]
    public async Task<ActionResult<MatchResultDto>> ProcessGameResult(Guid lobbyId, Guid gameId)
    {
        try
        {
            var result = await _phaseService.ProcessGameResultAsync(gameId, lobbyId);

            _logger.LogInformation("Game result processed for lobby {LobbyId}, game {GameId}. Match complete: {IsComplete}",
                lobbyId, gameId, result.IsMatchComplete);

            return Ok(new { success = true, data = result });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing game result for lobby {LobbyId}, game {GameId}", lobbyId, gameId);
            return StatusCode(500, new { message = "Failed to process game result", error = ex.Message });
        }
    }

    /// <summary>
    /// Get match result evaluation for a lobby
    /// </summary>
    [HttpGet("lobbies/{lobbyId}/match-result")]
    public async Task<ActionResult<MatchResultDto>> GetMatchResult(Guid lobbyId)
    {
        try
        {
            var result = await _phaseService.EvaluateMatchResultAsync(lobbyId);
            return Ok(new { success = true, data = result });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting match result for lobby {LobbyId}", lobbyId);
            return StatusCode(500, new { message = "Failed to get match result", error = ex.Message });
        }
    }



    /// <summary>
    /// Get phase lobby by code
    /// </summary>
    [HttpGet("lobbies/{lobbyCode}")]
    public async Task<ActionResult<CompetitionPhaseLobbyDto>> GetPhaseLobbyByCode(string lobbyCode)
    {
        try
        {
            var lobby = await _phaseService.GetPhaseLobbyByCodeAsync(lobbyCode);
            if (lobby == null)
                return NotFound(new { message = "Lobby not found" });
                
            return Ok(new { success = true, data = lobby });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting phase lobby by code {LobbyCode}", lobbyCode);
            return StatusCode(500, new { message = "Failed to get phase lobby", error = ex.Message });
        }
    }

    /// <summary>
    /// Delete a phase lobby
    /// </summary>
    [HttpDelete("lobbies/{lobbyId}")]
    public async Task<ActionResult> DeletePhaseLobby(Guid lobbyId)
    {
        try
        {
            await _phaseService.DeletePhaseLobbyAsync(lobbyId);
            return Ok(new { success = true, message = "Lobby deleted successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting phase lobby {LobbyId}", lobbyId);
            return StatusCode(500, new { message = "Failed to delete phase lobby", error = ex.Message });
        }
    }

    /// <summary>
    /// Set lobby winner
    /// </summary>
    [HttpPost("lobbies/{lobbyId}/winner")]
    public async Task<ActionResult> SetLobbyWinner(Guid lobbyId, [FromBody] SetLobbyWinnerDto dto)
    {
        try
        {
            await _phaseService.SetLobbyWinnerAsync(lobbyId, dto.WinnerTeamId);
            return Ok(new { success = true, message = "Lobby winner set successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting lobby winner for lobby {LobbyId}", lobbyId);
            return StatusCode(500, new { message = "Failed to set lobby winner", error = ex.Message });
        }
    }

    /// <summary>
    /// Get admin lobby view for competition management
    /// </summary>
    [HttpGet("admin/lobbies")]
    public async Task<ActionResult<List<AdminLobbyViewDto>>> GetAdminLobbyView(Guid competitionId, [FromQuery] string? phase = null)
    {
        try
        {
            var lobbies = await _phaseService.GetAdminLobbyViewAsync(competitionId, phase);
            return Ok(new { success = true, data = lobbies });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting admin lobby view for competition {CompetitionId}", competitionId);
            return StatusCode(500, new { message = "Failed to get admin lobby view", error = ex.Message });
        }
    }

    /// <summary>
    /// Get winners from a completed phase
    /// </summary>
    [HttpGet("{phase}/winners")]
    public async Task<ActionResult<List<CompetitionTeamDto>>> GetPhaseWinners(Guid competitionId, string phase)
    {
        try
        {
            var winners = await _phaseService.GetPhaseWinnersAsync(competitionId, phase);
            return Ok(new { success = true, data = winners });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting phase winners for competition {CompetitionId} phase {Phase}", competitionId, phase);
            return StatusCode(500, new { message = "Failed to get phase winners", error = ex.Message });
        }
    }

    /// <summary>
    /// Advance teams to next phase (Admin only)
    /// </summary>
    [HttpPost("{currentPhase}/advance/{nextPhase}")]
    public async Task<ActionResult> AdvancePhase(Guid competitionId, string currentPhase, string nextPhase)
    {
        try
        {
            // Check if phase can be advanced
            var canAdvance = await _phaseService.CanAdvancePhaseAsync(competitionId, currentPhase);
            if (!canAdvance)
            {
                return BadRequest(new { success = false, message = "Cannot advance phase. Some matches are still incomplete." });
            }

            await _phaseService.AdvancePhaseAsync(competitionId, currentPhase, nextPhase);

            _logger.LogInformation("Phase advanced for competition {CompetitionId} from {CurrentPhase} to {NextPhase}",
                competitionId, currentPhase, nextPhase);

            return Ok(new { success = true, message = $"Phase advanced from {currentPhase} to {nextPhase}" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error advancing phase for competition {CompetitionId}", competitionId);
            return StatusCode(500, new { message = "Failed to advance phase", error = ex.Message });
        }
    }

    /// <summary>
    /// Check if phase can be advanced
    /// </summary>
    [HttpGet("{phase}/can-advance")]
    public async Task<ActionResult<bool>> CanAdvancePhase(Guid competitionId, string phase)
    {
        try
        {
            var canAdvance = await _phaseService.CanAdvancePhaseAsync(competitionId, phase);
            return Ok(new { success = true, data = canAdvance });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking phase advancement for competition {CompetitionId} phase {Phase}", competitionId, phase);
            return StatusCode(500, new { message = "Failed to check phase advancement", error = ex.Message });
        }
    }

    /// <summary>
    /// Send match notification for a lobby
    /// </summary>
    [HttpPost("lobbies/{lobbyId}/notify")]
    public async Task<ActionResult> SendMatchNotification(Guid lobbyId, [FromQuery] string notificationType = "MatchScheduled")
    {
        try
        {
            await _phaseService.SendMatchNotificationAsync(lobbyId, notificationType);
            return Ok(new { success = true, message = "Notification sent successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending match notification for lobby {LobbyId}", lobbyId);
            return StatusCode(500, new { message = "Failed to send notification", error = ex.Message });
        }
    }

    /// <summary>
    /// Create multiple lobbies for a phase (bulk creation)
    /// </summary>
    [HttpPost("{phase}/lobbies/bulk")]
    public async Task<ActionResult<List<CompetitionPhaseLobbyDto>>> CreateMultipleLobbies(
        Guid competitionId,
        string phase,
        [FromBody] CreateMultipleLobbiesDto dto)
    {
        try
        {
            var adminId = Guid.Parse(User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? Guid.Empty.ToString());
            var lobbies = new List<CompetitionPhaseLobbyDto>();

            // Create lobbies for each team pairing
            for (int i = 0; i < dto.TeamPairings.Count; i++)
            {
                var pairing = dto.TeamPairings[i];
                var createDto = new CreateCompetitionPhaseLobbyDto
                {
                    CompetitionId = competitionId,
                    Phase = phase,
                    TeamIds = pairing.TeamIds,
                    MatchScheduledAt = dto.MatchScheduledAt,
                    BestOfGames = dto.BestOfGames,
                    RequiredWins = dto.RequiredWins,
                    SendNotification = dto.SendNotification
                };

                var lobby = await _phaseService.CreatePhaseLobbyAsync(createDto, adminId);
                lobbies.Add(lobby);
            }

            _logger.LogInformation("Created {Count} lobbies for competition {CompetitionId} phase {Phase}",
                lobbies.Count, competitionId, phase);

            return Ok(new { success = true, data = lobbies, message = $"Created {lobbies.Count} lobbies successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating multiple lobbies for competition {CompetitionId} phase {Phase}", competitionId, phase);
            return StatusCode(500, new { message = "Failed to create lobbies", error = ex.Message });
        }
    }

    /// <summary>
    /// Generate bracket for phase
    /// </summary>
    [HttpPost("bracket")]
    public async Task<ActionResult<BracketDto>> GenerateBracket(Guid competitionId, [FromQuery] string phase)
    {
        try
        {
            var bracket = await _phaseService.GenerateBracketAsync(competitionId, phase);
            return Ok(new { success = true, data = bracket });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating bracket for competition {CompetitionId} phase {Phase}", competitionId, phase);
            return StatusCode(500, new { message = "Failed to generate bracket", error = ex.Message });
        }
    }

    /// <summary>
    /// Get competition brackets
    /// </summary>
    [HttpGet("brackets")]
    public async Task<ActionResult<List<BracketDto>>> GetCompetitionBrackets(Guid competitionId)
    {
        try
        {
            var brackets = await _phaseService.GetCompetitionBracketsAsync(competitionId);
            return Ok(new { success = true, data = brackets });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting brackets for competition {CompetitionId}", competitionId);
            return StatusCode(500, new { message = "Failed to get brackets", error = ex.Message });
        }
    }

    /// <summary>
    /// Process phase end
    /// </summary>
    [HttpPost("process-end")]
    public async Task<ActionResult> ProcessPhaseEnd(Guid competitionId)
    {
        try
        {
            await _phaseService.ProcessPhaseEndAsync(competitionId);
            return Ok(new { success = true, message = "Phase end processed successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing phase end for competition {CompetitionId}", competitionId);
            return StatusCode(500, new { message = "Failed to process phase end", error = ex.Message });
        }
    }

    /// <summary>
    /// Check if competition can advance to next phase
    /// </summary>
    [HttpGet("can-advance")]
    public async Task<ActionResult<bool>> CanAdvanceToNextPhase(Guid competitionId)
    {
        try
        {
            var canAdvance = await _phaseService.CanAdvanceToNextPhaseAsync(competitionId);
            return Ok(new { success = true, data = canAdvance });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if competition {CompetitionId} can advance", competitionId);
            return StatusCode(500, new { message = "Failed to check advancement eligibility", error = ex.Message });
        }
    }

    /// <summary>
    /// Calculate phase rankings
    /// </summary>
    [HttpGet("rankings")]
    public async Task<ActionResult<List<CompetitionTeamDto>>> CalculatePhaseRankings(Guid competitionId, [FromQuery] string phase)
    {
        try
        {
            var rankings = await _phaseService.CalculatePhaseRankingsAsync(competitionId, phase);
            return Ok(new { success = true, data = rankings });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating rankings for competition {CompetitionId} phase {Phase}", competitionId, phase);
            return StatusCode(500, new { message = "Failed to calculate rankings", error = ex.Message });
        }
    }
}

public class AdvancePhaseDto
{
    public string NewPhase { get; set; } = string.Empty;
}

public class SetLobbyWinnerDto
{
    public Guid WinnerTeamId { get; set; }
}
