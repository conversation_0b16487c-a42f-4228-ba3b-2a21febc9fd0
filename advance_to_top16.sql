-- Advance competition phase from Top32 to Top16
-- Competition ID: 4860C19D-E3F3-4E2D-B359-275527461BD6

USE [GoldRushThunee]
GO

-- First, let's see the current state
SELECT 'BEFORE ADVANCEMENT TO TOP16' AS Status;
SELECT Phase, COUNT(*) AS TeamCount 
FROM CompetitionTeams 
WHERE CompetitionId = '4860C19D-E3F3-4E2D-B359-275527461BD6'
GROUP BY Phase;

-- Get top 16 teams from Top32 phase by total points (Points + BonusPoints)
SELECT TOP 16 
    Id,
    TeamName,
    Points,
    BonusPoints,
    (Points + BonusPoints) AS TotalPoints,
    GamesPlayed,
    RegisteredAt
FROM CompetitionTeams 
WHERE CompetitionId = '4860C19D-E3F3-4E2D-B359-275527461BD6'
  AND Phase = 'Top32'
  AND IsEliminated = 0
ORDER BY (Points + BonusPoints) DESC, Points DESC, GamesPlayed ASC, RegisteredAt ASC;

-- Get the team IDs as comma-separated string for the top 16 teams
DECLARE @EligibleTeamIds NVARCHAR(MAX);
SELECT @EligibleTeamIds = STRING_AGG(CAST(Id AS NVARCHAR(36)), ',')
FROM (
    SELECT TOP 16 Id
    FROM CompetitionTeams 
    WHERE CompetitionId = '4860C19D-E3F3-4E2D-B359-275527461BD6'
      AND Phase = 'Top32'
      AND IsEliminated = 0
    ORDER BY (Points + BonusPoints) DESC, Points DESC, GamesPlayed ASC, RegisteredAt ASC
) AS TopTeams;

PRINT 'Eligible Team IDs for Top16: ' + @EligibleTeamIds;

-- Execute the stored procedure
EXEC SP_AdvanceCompetitionPhase 
    @CompetitionId = '4860C19D-E3F3-4E2D-B359-275527461BD6',
    @NewPhase = 'Top16',
    @EligibleTeamIds = @EligibleTeamIds,
    @PhaseEndDate = '2025-08-15 23:59:59',
    @MaxGamesPerPhase = 3;

-- Check the results
SELECT 'AFTER ADVANCEMENT TO TOP16' AS Status;
SELECT Phase, COUNT(*) AS TeamCount 
FROM CompetitionTeams 
WHERE CompetitionId = '4860C19D-E3F3-4E2D-B359-275527461BD6'
GROUP BY Phase;

-- Show advanced teams
SELECT 'TOP16 TEAMS' AS Status;
SELECT TeamName, Points, BonusPoints, (Points + BonusPoints) AS TotalPoints, Phase, AdvancedToNextPhase
FROM CompetitionTeams 
WHERE CompetitionId = '4860C19D-E3F3-4E2D-B359-275527461BD6'
  AND Phase = 'Top16'
ORDER BY (Points + BonusPoints) DESC;

-- Check competition phase
SELECT 'COMPETITION STATUS' AS Status;
SELECT Phase, PhaseEndDate, MaxGamesPerPhase, UpdatedAt
FROM Competitions 
WHERE Id = '4860C19D-E3F3-4E2D-B359-275527461BD6';
