using ThuneeAPI.Application.DTOs;

namespace ThuneeAPI.Application.Interfaces;

public interface ICompetitionPhaseService
{
    // Phase Management
    Task<CompetitionDto> AdvanceCompetitionPhaseAsync(Guid competitionId, string newPhase);
    Task<List<CompetitionTeamDto>> GetTeamsForPhaseAsync(Guid competitionId, string phase);
    Task<List<CompetitionTeamDto>> GetEligibleTeamsForNextPhaseAsync(Guid competitionId);
    Task EliminateTeamsAsync(Guid competitionId, List<Guid> teamIds);
    Task AdvanceTeamsToNextPhaseAsync(Guid competitionId, List<Guid> teamIds);
    
    // Knockout Lobby Management
    Task<CompetitionPhaseLobbyDto> CreatePhaseLobbyAsync(CreateCompetitionPhaseLobbyDto createDto, Guid adminId);
    Task<List<CompetitionPhaseLobbyDto>> CreateAllPhaseMatchesAsync(Guid competitionId, string phase, DateTime startTime, int matchIntervalMinutes = 60, int bestOfGames = 3);
    Task<List<CompetitionPhaseLobbyDto>> GetPhaseLobbiesAsync(Guid competitionId, string phase);
    Task<CompetitionPhaseLobbyDto> GetPhaseLobbyByCodeAsync(string lobbyCode);
    Task<CompetitionPhaseLobbyDto> GetPhaseLobbyByIdAsync(Guid lobbyId);
    Task DeletePhaseLobbyAsync(Guid lobbyId);
    Task SetLobbyWinnerAsync(Guid lobbyId, Guid winnerTeamId);

    // Match Management
    Task ScheduleMatchAsync(Guid lobbyId, DateTime scheduledAt, string? customMessage = null);
    Task SendMatchNotificationAsync(Guid lobbyId, string notificationType = "MatchScheduled");
    Task<MatchResultDto> ProcessGameResultAsync(Guid gameId, Guid phaseLobbyId);
    Task<MatchResultDto> EvaluateMatchResultAsync(Guid lobbyId);
    Task<bool> IsMatchCompleteAsync(Guid lobbyId);

    // Admin Management
    Task<List<AdminLobbyViewDto>> GetAdminLobbyViewAsync(Guid competitionId, string? phase = null);
    Task<List<CompetitionTeamDto>> GetPhaseWinnersAsync(Guid competitionId, string phase);
    Task AdvancePhaseAsync(Guid competitionId, string currentPhase, string nextPhase);
    Task<bool> CanAdvancePhaseAsync(Guid competitionId, string phase);
    
    // Bracket Generation
    Task<BracketDto> GenerateBracketAsync(Guid competitionId, string phase);
    Task<List<BracketDto>> GetCompetitionBracketsAsync(Guid competitionId);
    
    // Phase Statistics
    Task<CompetitionTeamPhaseStatsDto> CreatePhaseStatsAsync(Guid competitionTeamId, string phase);
    Task<CompetitionTeamPhaseStatsDto> UpdatePhaseStatsAsync(Guid competitionTeamId, string phase, int points, int bonusPoints, int ballsWon);
    Task<List<CompetitionTeamPhaseStatsDto>> GetTeamPhaseStatsAsync(Guid competitionTeamId);
    
    // Phase Progression Logic
    Task ProcessPhaseEndAsync(Guid competitionId);
    Task<bool> CanAdvanceToNextPhaseAsync(Guid competitionId);
    Task<List<CompetitionTeamDto>> CalculatePhaseRankingsAsync(Guid competitionId, string phase);
}
