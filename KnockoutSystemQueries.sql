-- Helpful queries for viewing knockout tournament data
USE [GoldRushThunee]
GO

-- 1. View all knockout matches with team details
SELECT 
    cpl.LobbyCode,
    cpl.Phase,
    cpl.MatchStatus,
    cpl.MatchScheduledAt,
    cpl.BestOfGames,
    cpl.RequiredWins,
    cpl.CreatedAt,
    
    -- Team 1 details
    ct1.Team<PERSON>ame as Team1Name,
    u1.<PERSON><PERSON><PERSON> as Team1Player1,
    u2.<PERSON><PERSON><PERSON> as Team1Player2,
    
    -- Team 2 details  
    ct2.TeamName as Team2Name,
    u3.<PERSON>rname as Team2Player1,
    u4.<PERSON>rname as Team2Player2,
    
    -- Match progress
    CASE 
        WHEN cpl.MatchStatus = 'Completed' THEN 'Match Complete'
        ELSE CONCAT('Games needed: ', cpl.RequiredWins)
    END as MatchProgress

FROM CompetitionPhaseLobbies cpl
LEFT JOIN CompetitionPhaseLobbyTeams cplt1 ON cpl.Id = cplt1.LobbyId 
LEFT JOIN CompetitionPhaseLobbyTeams cplt2 ON cpl.Id = cplt2.LobbyId AND cplt2.Id != cplt1.Id
LEFT JOIN CompetitionTeams ct1 ON cplt1.CompetitionTeamId = ct1.Id
LEFT JOIN CompetitionTeams ct2 ON cplt2.CompetitionTeamId = ct2.Id
LEFT JOIN Users u1 ON ct1.Player1Id = u1.Id
LEFT JOIN Users u2 ON ct1.Player2Id = u2.Id  
LEFT JOIN Users u3 ON ct2.Player1Id = u3.Id
LEFT JOIN Users u4 ON ct2.Player2Id = u4.Id
WHERE cpl.Phase LIKE '%32%' OR cpl.Phase LIKE '%16%' OR cpl.Phase LIKE '%Quarter%' OR cpl.Phase LIKE '%Semi%' OR cpl.Phase LIKE '%Final%'
ORDER BY cpl.MatchScheduledAt, cpl.LobbyCode;

-- 2. Count matches by phase and status
SELECT 
    Phase,
    MatchStatus,
    COUNT(*) as MatchCount,
    AVG(BestOfGames) as AvgBestOf
FROM CompetitionPhaseLobbies 
GROUP BY Phase, MatchStatus
ORDER BY Phase, MatchStatus;

-- 3. View teams ready for knockout (advanced to next phase)
SELECT 
    ct.TeamName,
    ct.Phase,
    ct.Points,
    ct.BonusPoints,
    ct.GamesPlayed,
    ct.AdvancedToNextPhase,
    ct.IsEliminated,
    u1.Username as Player1,
    u2.Username as Player2
FROM CompetitionTeams ct
LEFT JOIN Users u1 ON ct.Player1Id = u1.Id
LEFT JOIN Users u2 ON ct.Player2Id = u2.Id
WHERE ct.AdvancedToNextPhase = 1 AND ct.IsEliminated = 0
ORDER BY ct.Points DESC, ct.BonusPoints DESC;

-- 4. View upcoming scheduled matches
SELECT 
    cpl.LobbyCode,
    cpl.Phase,
    cpl.MatchScheduledAt,
    cpl.BestOfGames,
    ct1.TeamName + ' vs ' + ct2.TeamName as Matchup,
    DATEDIFF(MINUTE, GETUTCDATE(), cpl.MatchScheduledAt) as MinutesUntilMatch
FROM CompetitionPhaseLobbies cpl
LEFT JOIN CompetitionPhaseLobbyTeams cplt1 ON cpl.Id = cplt1.LobbyId 
LEFT JOIN CompetitionPhaseLobbyTeams cplt2 ON cpl.Id = cplt2.LobbyId AND cplt2.Id != cplt1.Id
LEFT JOIN CompetitionTeams ct1 ON cplt1.CompetitionTeamId = ct1.Id
LEFT JOIN CompetitionTeams ct2 ON cplt2.CompetitionTeamId = ct2.Id
WHERE cpl.MatchStatus = 'Scheduled' 
  AND cpl.MatchScheduledAt > GETUTCDATE()
ORDER BY cpl.MatchScheduledAt;

-- 5. Test data - Insert some sample teams for testing (optional)
/*
-- Uncomment to create test data
INSERT INTO CompetitionTeams (CompetitionId, TeamName, Player1Id, Player2Id, Phase, Points, AdvancedToNextPhase, IsEliminated)
VALUES 
    (NEWID(), 'Team Alpha', NEWID(), NEWID(), 'Top32', 100, 1, 0),
    (NEWID(), 'Team Beta', NEWID(), NEWID(), 'Top32', 95, 1, 0),
    (NEWID(), 'Team Gamma', NEWID(), NEWID(), 'Top32', 90, 1, 0),
    (NEWID(), 'Team Delta', NEWID(), NEWID(), 'Top32', 85, 1, 0);
*/
