2025-08-04 10:51:26.942 +02:00 [INF] Starting Thunee API Server
2025-08-04 10:58:38.199 +02:00 [INF] Getting admin lobby view for competition "4860c19d-e3f3-4e2d-b359-275527461bd6" phase null
2025-08-04 10:59:21.714 +02:00 [INF] Getting admin lobby view for competition "4860c19d-e3f3-4e2d-b359-275527461bd6" phase null
2025-08-04 12:34:42.528 +02:00 [INF] Login attempt for username: t2p1
2025-08-04 12:34:47.924 +02:00 [WRN] <PERSON><PERSON> failed for username t2p1: Invalid username or password
2025-08-04 12:35:02.446 +02:00 [INF] Login attempt for username: t2p1
2025-08-04 12:35:07.137 +02:00 [WRN] <PERSON><PERSON> failed for username t2p1: Invalid username or password
2025-08-04 12:35:11.731 +02:00 [INF] Login attempt for username: t2p1
2025-08-04 12:35:14.839 +02:00 [INF] User logged in successfully: "9f781650-de07-4109-b134-2272d51e2962"
2025-08-04 12:56:26.230 +02:00 [INF] Starting Thunee API Server
2025-08-04 12:56:45.457 +02:00 [INF] Login attempt for username: Sherisan123
2025-08-04 12:56:47.608 +02:00 [INF] User logged in successfully: "ee9511df-a34b-46ec-b0b7-8bcd0e24373f"
2025-08-04 12:58:17.627 +02:00 [INF] Getting admin lobby view for competition "4860c19d-e3f3-4e2d-b359-275527461bd6" phase null
2025-08-04 12:58:53.941 +02:00 [INF] Getting admin lobby view for competition "4860c19d-e3f3-4e2d-b359-275527461bd6" phase null
2025-08-04 12:59:34.472 +02:00 [INF] Creating all matches for competition "4860c19d-e3f3-4e2d-b359-275527461bd6" phase Top32
2025-08-04 12:59:34.477 +02:00 [INF] Executing SP_CreateAllPhaseMatches for Top32 starting at "2025-08-05T11:20:00.0000000Z"
2025-08-04 12:59:35.152 +02:00 [ERR] Error creating all phase matches for competition "4860c19d-e3f3-4e2d-b359-275527461bd6" phase Top32
Microsoft.Data.SqlClient.SqlException (0x80131904): Could not find stored procedure 'SP_CreateAllPhaseMatches'.
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Dapper.SqlMapper.QueryAsync[T](IDbConnection cnn, Type effectiveType, CommandDefinition command) in /_/Dapper/SqlMapper.Async.cs:line 434
   at ThuneeAPI.Infrastructure.Data.BaseRepository.ExecuteStoredProcedureAsync[T](String storedProcedureName, Object parameters) in C:\Users\<USER>\source\repos\Thunee-fe\ThuneeAPI\ThuneeAPI.Infrastructure\Data\BaseRepository.cs:line 30
   at ThuneeAPI.Infrastructure.Data.Repositories.CompetitionRepository.ExecuteStoredProcedureAsync[T](String storedProcedureName, Object parameters) in C:\Users\<USER>\source\repos\Thunee-fe\ThuneeAPI\ThuneeAPI.Infrastructure\Data\Repositories\CompetitionRepository.cs:line 145
   at ThuneeAPI.Infrastructure.Services.CompetitionPhaseService.CreateAllPhaseMatchesAsync(Guid competitionId, String phase, Guid adminId, DateTime startTime, Int32 matchIntervalMinutes, Int32 bestOfGames) in C:\Users\<USER>\source\repos\Thunee-fe\ThuneeAPI\ThuneeAPI.Infrastructure\Services\CompetitionPhaseService.cs:line 398
ClientConnectionId:7f0b9e92-bb9d-4528-8198-1e51ef144209
Error Number:2812,State:62,Class:16
2025-08-04 12:59:35.199 +02:00 [ERR] Error creating all phase matches for competition "4860c19d-e3f3-4e2d-b359-275527461bd6" phase Top32
Microsoft.Data.SqlClient.SqlException (0x80131904): Could not find stored procedure 'SP_CreateAllPhaseMatches'.
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Dapper.SqlMapper.QueryAsync[T](IDbConnection cnn, Type effectiveType, CommandDefinition command) in /_/Dapper/SqlMapper.Async.cs:line 434
   at ThuneeAPI.Infrastructure.Data.BaseRepository.ExecuteStoredProcedureAsync[T](String storedProcedureName, Object parameters) in C:\Users\<USER>\source\repos\Thunee-fe\ThuneeAPI\ThuneeAPI.Infrastructure\Data\BaseRepository.cs:line 30
   at ThuneeAPI.Infrastructure.Data.Repositories.CompetitionRepository.ExecuteStoredProcedureAsync[T](String storedProcedureName, Object parameters) in C:\Users\<USER>\source\repos\Thunee-fe\ThuneeAPI\ThuneeAPI.Infrastructure\Data\Repositories\CompetitionRepository.cs:line 145
   at ThuneeAPI.Infrastructure.Services.CompetitionPhaseService.CreateAllPhaseMatchesAsync(Guid competitionId, String phase, Guid adminId, DateTime startTime, Int32 matchIntervalMinutes, Int32 bestOfGames) in C:\Users\<USER>\source\repos\Thunee-fe\ThuneeAPI\ThuneeAPI.Infrastructure\Services\CompetitionPhaseService.cs:line 398
   at ThuneeAPI.Controllers.CompetitionPhaseController.CreateAllPhaseMatches(Guid competitionId, String phase, CreateAllPhaseMatchesDto dto) in C:\Users\<USER>\source\repos\Thunee-fe\ThuneeAPI\ThuneeAPI\Controllers\CompetitionPhaseController.cs:line 441
ClientConnectionId:7f0b9e92-bb9d-4528-8198-1e51ef144209
Error Number:2812,State:62,Class:16
