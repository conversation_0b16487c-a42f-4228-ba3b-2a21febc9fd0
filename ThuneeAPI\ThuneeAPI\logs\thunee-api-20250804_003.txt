2025-08-04 13:18:11.066 +02:00 [INF] Starting Thunee API Server
2025-08-04 13:46:48.167 +02:00 [INF] Getting admin lobby view for competition "4860c19d-e3f3-4e2d-b359-275527461bd6" phase null
2025-08-04 13:47:27.548 +02:00 [INF] Getting admin lobby view for competition "4860c19d-e3f3-4e2d-b359-275527461bd6" phase null
2025-08-04 13:50:49.340 +02:00 [INF] Creating all matches for competition "4860c19d-e3f3-4e2d-b359-275527461bd6" phase Top32
2025-08-04 13:50:49.345 +02:00 [INF] Executing SP_CreateAllPhaseMatches for Top32 starting at "2025-08-05T12:00:00.0000000Z"
2025-08-04 13:50:51.013 +02:00 [ERR] Error creating all phase matches for competition "4860c19d-e3f3-4e2d-b359-275527461bd6" phase Top32
Microsoft.Data.SqlClient.SqlException (0x80131904): Procedure or function SP_CreateAllPhaseMatches has too many arguments specified.
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Dapper.SqlMapper.QueryAsync[T](IDbConnection cnn, Type effectiveType, CommandDefinition command) in /_/Dapper/SqlMapper.Async.cs:line 434
   at ThuneeAPI.Infrastructure.Data.BaseRepository.ExecuteStoredProcedureAsync[T](String storedProcedureName, Object parameters) in C:\Users\<USER>\source\repos\Thunee-fe\ThuneeAPI\ThuneeAPI.Infrastructure\Data\BaseRepository.cs:line 30
   at ThuneeAPI.Infrastructure.Data.Repositories.CompetitionRepository.ExecuteStoredProcedureAsync[T](String storedProcedureName, Object parameters) in C:\Users\<USER>\source\repos\Thunee-fe\ThuneeAPI\ThuneeAPI.Infrastructure\Data\Repositories\CompetitionRepository.cs:line 145
   at ThuneeAPI.Infrastructure.Services.CompetitionPhaseService.CreateAllPhaseMatchesAsync(Guid competitionId, String phase, Guid adminId, DateTime startTime, Int32 matchIntervalMinutes, Int32 bestOfGames) in C:\Users\<USER>\source\repos\Thunee-fe\ThuneeAPI\ThuneeAPI.Infrastructure\Services\CompetitionPhaseService.cs:line 398
ClientConnectionId:f626d893-f73a-4537-9134-917e58a5dde7
Error Number:8144,State:2,Class:16
2025-08-04 13:50:51.044 +02:00 [ERR] Error creating all phase matches for competition "4860c19d-e3f3-4e2d-b359-275527461bd6" phase Top32
Microsoft.Data.SqlClient.SqlException (0x80131904): Procedure or function SP_CreateAllPhaseMatches has too many arguments specified.
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Dapper.SqlMapper.QueryAsync[T](IDbConnection cnn, Type effectiveType, CommandDefinition command) in /_/Dapper/SqlMapper.Async.cs:line 434
   at ThuneeAPI.Infrastructure.Data.BaseRepository.ExecuteStoredProcedureAsync[T](String storedProcedureName, Object parameters) in C:\Users\<USER>\source\repos\Thunee-fe\ThuneeAPI\ThuneeAPI.Infrastructure\Data\BaseRepository.cs:line 30
   at ThuneeAPI.Infrastructure.Data.Repositories.CompetitionRepository.ExecuteStoredProcedureAsync[T](String storedProcedureName, Object parameters) in C:\Users\<USER>\source\repos\Thunee-fe\ThuneeAPI\ThuneeAPI.Infrastructure\Data\Repositories\CompetitionRepository.cs:line 145
   at ThuneeAPI.Infrastructure.Services.CompetitionPhaseService.CreateAllPhaseMatchesAsync(Guid competitionId, String phase, Guid adminId, DateTime startTime, Int32 matchIntervalMinutes, Int32 bestOfGames) in C:\Users\<USER>\source\repos\Thunee-fe\ThuneeAPI\ThuneeAPI.Infrastructure\Services\CompetitionPhaseService.cs:line 398
   at ThuneeAPI.Controllers.CompetitionPhaseController.CreateAllPhaseMatches(Guid competitionId, String phase, CreateAllPhaseMatchesDto dto) in C:\Users\<USER>\source\repos\Thunee-fe\ThuneeAPI\ThuneeAPI\Controllers\CompetitionPhaseController.cs:line 441
ClientConnectionId:f626d893-f73a-4537-9134-917e58a5dde7
Error Number:8144,State:2,Class:16
2025-08-04 14:00:38.614 +02:00 [INF] Starting Thunee API Server
