-- Advance competition phase from Top8 to Top4
-- Competition ID: 4860C19D-E3F3-4E2D-B359-275527461BD6

USE [GoldRushThunee]
GO

-- First, let's see the current state
SELECT 'BEFORE ADVANCEMENT TO TOP4' AS Status;
SELECT Phase, COUNT(*) AS TeamCount 
FROM CompetitionTeams 
WHERE CompetitionId = '4860C19D-E3F3-4E2D-B359-275527461BD6'
GROUP BY Phase;

-- Get top 4 teams from Top8 phase by total points (Points + BonusPoints)
SELECT TOP 4 
    Id,
    TeamName,
    Points,
    BonusPoints,
    (Points + BonusPoints) AS TotalPoints,
    GamesPlayed,
    RegisteredAt
FROM CompetitionTeams 
WHERE CompetitionId = '4860C19D-E3F3-4E2D-B359-275527461BD6'
  AND Phase = 'Top8'
  AND IsEliminated = 0
ORDER BY (Points + BonusPoints) DESC, Points DESC, GamesPlayed ASC, RegisteredAt ASC;

-- Get the team IDs as comma-separated string for the top 4 teams
DECLARE @EligibleTeamIds NVARCHAR(MAX);
SELECT @EligibleTeamIds = STRING_AGG(CAST(Id AS NVARCHAR(36)), ',')
FROM (
    SELECT TOP 4 Id
    FROM CompetitionTeams 
    WHERE CompetitionId = '4860C19D-E3F3-4E2D-B359-275527461BD6'
      AND Phase = 'Top8'
      AND IsEliminated = 0
    ORDER BY (Points + BonusPoints) DESC, Points DESC, GamesPlayed ASC, RegisteredAt ASC
) AS TopTeams;

PRINT 'Eligible Team IDs for Top4: ' + @EligibleTeamIds;

-- Execute the stored procedure
EXEC SP_AdvanceCompetitionPhase 
    @CompetitionId = '4860C19D-E3F3-4E2D-B359-275527461BD6',
    @NewPhase = 'Top4',
    @EligibleTeamIds = @EligibleTeamIds,
    @PhaseEndDate = '2025-08-25 23:59:59',
    @MaxGamesPerPhase = 3;

-- Check the results
SELECT 'AFTER ADVANCEMENT TO TOP4' AS Status;
SELECT Phase, COUNT(*) AS TeamCount 
FROM CompetitionTeams 
WHERE CompetitionId = '4860C19D-E3F3-4E2D-B359-275527461BD6'
GROUP BY Phase;

-- Show advanced teams
SELECT 'TOP4 TEAMS' AS Status;
SELECT TeamName, Points, BonusPoints, (Points + BonusPoints) AS TotalPoints, Phase, AdvancedToNextPhase
FROM CompetitionTeams 
WHERE CompetitionId = '4860C19D-E3F3-4E2D-B359-275527461BD6'
  AND Phase = 'Top4'
ORDER BY (Points + BonusPoints) DESC;

-- Check competition phase
SELECT 'COMPETITION STATUS' AS Status;
SELECT Phase, PhaseEndDate, MaxGamesPerPhase, UpdatedAt
FROM Competitions 
WHERE Id = '4860C19D-E3F3-4E2D-B359-275527461BD6';
